// Top‑level build.gradle

buildscript {
    repositories {
        google()
        mavenCentral()
        // Ajout JitPack pour que les plugins et dépendances GitHub soient trouvés
        maven { url 'https://jitpack.io' }
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:7.4.2'
        // Note : vos dépendances d'application restent dans les build.gradle des modules
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
        // Vous pouvez conserver jcenter() tant que nécessaire, mais préférez Maven Central + JitPack
        jcenter() // (optionnel)
        maven { url 'https://jitpack.io' }
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}

package com.reeman.robot.disinfection;

import com.elvishew.xlog.XLog;
import com.reeman.robot.disinfection.utils.TimeUtils;

import org.junit.Test;

import java.util.Date;

import static org.junit.Assert.*;

/**
 * Example local unit test, which will execute on the development machine (host).
 *
 * @see <a href="http://d.android.com/tools/testing">Testing documentation</a>
 */
public class ExampleUnitTest {
    @Test
    public void addition_isCorrect() {
        assertEquals(4, 2 + 2);
    }

    @Test
    public void getTime(){
        System.out.println(TimeUtils.formatHourAndMinute(new Date()));
    }
}
<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_main_background"
    android:orientation="vertical"
    android:paddingLeft="@dimen/pd_xl"
    android:paddingTop="@dimen/pd_sm"
    android:paddingRight="@dimen/pd_xl"
    android:paddingBottom="@dimen/pd_xl"
    tools:context=".activities.TaskExecutingActivity">

    <!--头部状态栏-->
    <RelativeLayout
        android:id="@+id/rl_status_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingLeft="@dimen/pd_sm"
        android:paddingRight="@dimen/pd_sm">


        <TextView
            android:id="@+id/tv_hostname"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:drawableStart="@drawable/img_robot_host_name"
            android:drawablePadding="@dimen/pd_md"
            android:padding="@dimen/pd_sm"
            android:textColor="@android:color/white"
            android:textSize="@dimen/ts_xl" />


        <TextView
            android:id="@+id/tv_power"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:drawablePadding="@dimen/pd_sm"
            android:gravity="center"
            android:textAlignment="center"
            android:textColor="@android:color/white"
            android:textSize="@dimen/ts_xl" />

    </RelativeLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="@dimen/mr_sm">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="@drawable/img_robot_state_background"
            android:gravity="center_horizontal"
            android:orientation="vertical"
            android:paddingTop="30dp">

            <TextView
                android:id="@+id/tv_machine_state"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:text="@string/text_title_robot_state"
                android:textColor="@android:color/white"
                android:textSize="@dimen/ts_common"
                android:textStyle="bold" />


            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/mr_md"
                android:gravity="center"
                android:orientation="vertical"
                android:paddingLeft="@dimen/pd_sm"
                android:paddingRight="@dimen/pd_sm">

                <TextView
                    android:id="@+id/tv_current_task_type"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="start"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_xl" />

                <TextView
                    android:visibility="gone"
                    android:id="@+id/tv_current_task_mode"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="start"
                    android:layout_marginTop="@dimen/mr_md"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_xl" />

                <TextView
                    android:id="@+id/tv_current_task_start_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="start"
                    android:layout_marginTop="@dimen/mr_md"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_xl" />


                <TextView
                    android:id="@+id/tv_current_task_time_waste"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="start"
                    android:layout_marginTop="@dimen/mr_md"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_xl" />

                <TextView
                    android:visibility="gone"
                    android:id="@+id/tv_current_task_time_remain"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="start"
                    android:layout_marginTop="@dimen/mr_md"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_xl" />

                <TextView
                    android:id="@+id/tv_current_disinfection_switch_state"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="start"
                    android:layout_marginTop="@dimen/mr_md"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_xl" />

                <TextView
                    android:id="@+id/tv_current_destination"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="start"
                    android:layout_marginTop="@dimen/mr_md"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_xl" />
                <TextView
                    android:id="@+id/tv_elevator_state"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="start"
                    android:layout_marginTop="@dimen/mr_md"
                    android:textColor="@color/white"
                    android:textSize="@dimen/ts_xl"
                    />
            </LinearLayout>


        </LinearLayout>

        <!--右侧-->
        <RelativeLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginStart="@dimen/mr_xl"
            android:layout_weight="1.5"
            android:background="@drawable/img_manual_task_background"
            android:orientation="vertical"
            android:paddingTop="30dp"
            android:paddingBottom="20dp">

            <TextView
                android:id="@+id/tv_task_state"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:text="@string/text_title_task_state"
                android:textColor="@android:color/white"
                android:textSize="@dimen/ts_common"
                android:textStyle="bold" />


            <com.reeman.robot.disinfection.widgets.FourCircleRotate
                android:layout_width="200dp"
                android:layout_height="200dp"
                android:layout_below="@id/tv_task_state"
                android:layout_centerInParent="true"
                android:layout_marginTop="@dimen/mr_lg" />


            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:layout_centerHorizontal="true">

                <Button
                    android:id="@+id/btn_pause_and_resume_disinfection"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="60dp"
                    android:layout_marginBottom="@dimen/mr_lg"
                    android:background="@drawable/bg_btn_start_task"
                    android:padding="@dimen/pd_md"
                    android:text="@string/text_pause_task"
                    android:textAllCaps="false"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_common" />

                <Button
                    android:id="@+id/btn_finish_task"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/mr_lg"
                    android:background="@drawable/bg_btn_start_task"
                    android:padding="@dimen/pd_md"
                    android:text="@string/text_finish_task"
                    android:textAllCaps="false"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_common" />
            </LinearLayout>

        </RelativeLayout>
    </LinearLayout>
</LinearLayout>

<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_horizontal"
    android:orientation="vertical"
    android:paddingTop="@dimen/pd_xl"
    android:paddingBottom="@dimen/pd_lg">

    <RelativeLayout
        android:layout_width="500dp"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_outline_input"
        android:gravity="center_vertical">

        <LinearLayout
            android:id="@+id/ll_lock_screen_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_toStartOf="@id/tv_confirm"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageButton
                android:id="@+id/ib_show_password"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@null"
                android:padding="@dimen/pd_md"
                android:src="@drawable/icon_show_pass" />

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/et_lock_screen"
                android:layout_width="0dp"
                android:layout_height="70dp"
                android:layout_weight="1"
                android:background="@null"
                android:focusable="false"
                android:focusableInTouchMode="false"
                android:hint="@string/text_enter_lock_screen_password"
                android:inputType="textPassword"
                android:textColor="@color/color_document_grey"
                android:textColorHint="@color/color_document_grey"
                android:textSize="@dimen/ts_md" />

            <ImageButton
                android:id="@+id/ib_roll_back"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@null"
                android:paddingLeft="@dimen/pd_sm"
                android:paddingTop="@dimen/pd_md"
                android:paddingRight="@dimen/pd_sm"
                android:paddingBottom="@dimen/pd_md"
                android:src="@drawable/icon_clear"
                android:visibility="invisible" />

        </LinearLayout>

        <TextView
            android:id="@+id/tv_confirm"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:gravity="end"
            android:paddingLeft="@dimen/pd_lg"
            android:paddingTop="@dimen/pd_md"
            android:paddingRight="@dimen/pd_lg"
            android:paddingBottom="@dimen/pd_md"
            android:text="@string/text_confirm"
            android:textColor="@color/color_document_grey"
            android:textSize="@dimen/ts_lg" />
    </RelativeLayout>


    <GridLayout
        android:id="@+id/gl_lock_screen_keys"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/mr_md"
        android:columnCount="3"
        android:rowCount="3">

        <Button
            android:id="@+id/btn_key_1"
            android:layout_width="160dp"
            android:layout_height="90dp"
            android:layout_margin="@dimen/mr_xs"
            android:layout_marginBottom="@dimen/mr_sm"
            android:background="#CC489EFF"
            android:text="1"
            android:textAlignment="center"
            android:textColor="@android:color/white"
            android:textSize="@dimen/ts_common" />

        <Button
            android:id="@+id/btn_key_2"
            android:layout_width="160dp"
            android:layout_height="90dp"
            android:layout_margin="@dimen/mr_xs"
            android:layout_marginBottom="@dimen/mr_sm"
            android:background="#CC489EFF"
            android:text="2"
            android:textAlignment="center"
            android:textColor="@android:color/white"
            android:textSize="@dimen/ts_common" />

        <Button
            android:id="@+id/btn_key_3"
            android:layout_width="160dp"
            android:layout_height="90dp"
            android:layout_margin="@dimen/mr_xs"
            android:layout_marginBottom="@dimen/mr_sm"
            android:background="#CC489EFF"
            android:text="3"
            android:textAlignment="center"
            android:textColor="@android:color/white"
            android:textSize="@dimen/ts_common" />

        <Button
            android:id="@+id/btn_key_4"
            android:layout_width="160dp"
            android:layout_height="90dp"
            android:layout_margin="@dimen/mr_xs"
            android:layout_marginBottom="@dimen/mr_sm"
            android:background="#CC489EFF"
            android:text="4"
            android:textAlignment="center"
            android:textColor="@android:color/white"
            android:textSize="@dimen/ts_common" />

        <Button
            android:id="@+id/btn_key_5"
            android:layout_width="160dp"
            android:layout_height="90dp"
            android:layout_margin="@dimen/mr_xs"
            android:layout_marginBottom="@dimen/mr_sm"
            android:background="#CC489EFF"
            android:text="5"
            android:textAlignment="center"
            android:textColor="@android:color/white"
            android:textSize="@dimen/ts_common" />

        <Button
            android:id="@+id/btn_key_6"
            android:layout_width="160dp"
            android:layout_height="90dp"
            android:layout_margin="@dimen/mr_xs"
            android:layout_marginBottom="@dimen/mr_sm"
            android:background="#CC489EFF"
            android:text="6"
            android:textAlignment="center"
            android:textColor="@android:color/white"
            android:textSize="@dimen/ts_common" />

        <Button
            android:id="@+id/btn_key_7"
            android:layout_width="160dp"
            android:layout_height="90dp"
            android:layout_margin="@dimen/mr_xs"
            android:layout_marginBottom="@dimen/mr_sm"
            android:background="#CC489EFF"
            android:text="7"
            android:textAlignment="center"
            android:textColor="@android:color/white"
            android:textSize="@dimen/ts_common" />

        <Button
            android:id="@+id/btn_key_8"
            android:layout_width="160dp"
            android:layout_height="90dp"
            android:layout_margin="@dimen/mr_xs"
            android:layout_marginBottom="@dimen/mr_sm"
            android:background="#CC489EFF"
            android:text="8"
            android:textAlignment="center"
            android:textColor="@android:color/white"
            android:textSize="@dimen/ts_common" />

        <Button
            android:id="@+id/btn_key_9"
            android:layout_width="160dp"
            android:layout_height="90dp"
            android:layout_margin="@dimen/mr_xs"
            android:layout_marginBottom="@dimen/mr_sm"
            android:background="#CC489EFF"
            android:text="9"
            android:textAlignment="center"
            android:textColor="@android:color/white"
            android:textSize="@dimen/ts_common" />

    </GridLayout>

</LinearLayout>
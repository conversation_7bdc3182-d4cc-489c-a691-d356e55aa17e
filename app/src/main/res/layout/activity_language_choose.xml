<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#0066aa"
    android:padding="@dimen/pd_md"
    tools:context=".activities.LanguageChooseActivity">

    <ImageButton
        android:id="@+id/tv_back"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@null"
        android:paddingStart="@dimen/pd_md"
        android:paddingTop="@dimen/pd_sm"
        android:paddingEnd="@dimen/pd_md"
        android:paddingBottom="@dimen/pd_sm"
        android:src="@drawable/ic_back"
        android:text="@string/text_back"
        android:textColor="@android:color/white"
        android:textSize="@dimen/ts_common" />

    <ListView
        android:id="@+id/lv_language_list"
        android:layout_width="600dp"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:layout_marginTop="@dimen/mr_md"
        android:divider="@drawable/line_list_divider"
        android:dividerHeight="3dp" />

    <Button
        android:id="@+id/btn_confirm"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_alignParentBottom="true"
        android:layout_marginEnd="@dimen/mr_sm"
        android:layout_marginBottom="@dimen/mr_sm"
        android:background="@drawable/bg_common_button_white"
        android:paddingLeft="@dimen/pd_md"
        android:paddingTop="@dimen/pd_sm"
        android:paddingRight="@dimen/pd_md"
        android:paddingBottom="@dimen/pd_sm"
        android:textAllCaps="false"
        android:textColor="#152449"
        android:textSize="@dimen/ts_md" />

</RelativeLayout>
<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/color_main_background"
    android:orientation="vertical"
    android:padding="@dimen/pd_md"
    android:paddingStart="@dimen/pd_xl"
    android:paddingEnd="@dimen/pd_xl">

    <TextView
        android:id="@+id/tv_back"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:drawableStart="@drawable/ic_back"
        android:padding="@dimen/pd_sm"
        android:text="@string/text_back"
        android:textColor="@android:color/white"
        android:textSize="@dimen/ts_xl" />

    <TextView
        android:id="@+id/tv_port_mapping_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:padding="@dimen/pd_sm"
        android:text="@string/text_please_choose_floor"
        android:textColor="@color/white"
        android:textSize="@dimen/ts_common" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/tv_port_mapping_title"
        android:background="@drawable/img_content_background">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_port_mapping_list"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

    </androidx.core.widget.NestedScrollView>

    <ImageButton
        android:id="@+id/ib_add"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="@dimen/mr_md"
        android:background="@drawable/bg_add_task"
        android:src="@drawable/icon_new_task"
        app:fabSize="normal" />

</RelativeLayout>
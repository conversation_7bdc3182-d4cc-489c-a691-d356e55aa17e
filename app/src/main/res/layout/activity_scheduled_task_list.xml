<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/ll_root"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_main_background"
    android:orientation="vertical"
    android:paddingLeft="40dp"
    android:paddingTop="@dimen/pd_sm"
    android:paddingRight="40dp"
    android:paddingBottom="@dimen/pd_xl"
    tools:context=".activities.ScheduledTaskListActivity">


    <RelativeLayout
        android:id="@+id/rl_top_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="@dimen/pd_sm"
        android:paddingBottom="@dimen/pd_sm">

        <TextView
            android:id="@+id/tv_back"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawableStart="@drawable/ic_back"
            android:padding="@dimen/pd_sm"
            android:text="@string/text_back"
            android:textColor="@android:color/white"
            android:textSize="@dimen/ts_xl" />

        <TextView
            android:id="@+id/tv_scheduled_task_list"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:text="@string/text_title_scheduled_task_list"
            android:textColor="@android:color/white"
            android:textSize="@dimen/ts_common"
            android:textStyle="bold" />

    </RelativeLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/rl_top_bar">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_task_list"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />
    </androidx.core.widget.NestedScrollView>

    <ImageButton
        android:id="@+id/ib_new_task"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="@dimen/mr_md"
        android:background="@drawable/bg_add_task"
        android:src="@drawable/icon_new_task"
        app:fabSize="normal" />

</RelativeLayout>
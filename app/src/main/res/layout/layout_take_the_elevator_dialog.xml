<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="660dp"
    android:layout_height="wrap_content">

    <RelativeLayout
        android:layout_width="0dp"
        android:layout_height="350dp"
        android:layout_weight="3">

       <com.gongwen.marqueen.MarqueeView
        android:id="@+id/mv_elevator"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:layout_marginStart="30dp"
        android:layout_marginEnd="30dp"
        android:flipInterval="2500"
        android:inAnimation="@anim/in_bottom"
        android:outAnimation="@anim/out_top"
        app:mvAnimDuration="3500" />  <!-- <PERSON><PERSON> SURE IT LOOKS LIKE THIS -->

    </RelativeLayout>

    <View
        android:layout_width="1dp"
        android:layout_height="match_parent"
        android:background="#33000000"

        />

    <RelativeLayout
        android:layout_width="0dp"
        android:layout_height="350dp"
        android:layout_weight="7"
        >

        <LinearLayout
            android:id="@+id/fl_dialog_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="20dp"
            android:gravity="center"
            android:layout_centerInParent="true"
            android:orientation="horizontal"
            android:minHeight="80dp"
            android:padding="10dp">

            <TextView
                android:id="@+id/tv_content"
                android:layout_width="wrap_content"
                android:layout_height="match_parent"
                android:layout_marginStart="20dp"
                android:layout_marginEnd="20dp"
                android:lineSpacingExtra="6dp"

                android:textColor="#666"
                android:textSize="26sp" />

        </LinearLayout>

            <Button
                android:id="@+id/btn_close"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="20dp"
                android:layout_marginBottom="20dp"
                android:layout_alignParentEnd="true"
                android:layout_alignParentBottom="true"
                android:background="@drawable/bg_common_button_red"
                android:text="@string/text_cancel_task"
                android:textAllCaps="false"
                android:textColor="@android:color/white"
                android:textSize="22sp" />



    </RelativeLayout>

</LinearLayout>
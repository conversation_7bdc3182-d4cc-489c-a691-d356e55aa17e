<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center"
    android:orientation="vertical"
    >

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/pd_lg">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/text_port"
                android:textColor="@color/white"
                android:textSize="@dimen/ts_xl" />

            <EditText
                android:id="@+id/et_port"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="2"
                android:hint="@string/text_please_input_port"
                android:textColor="@color/white"
                android:inputType="number"
                android:textSize="@dimen/ts_xl" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/text_floor"
                android:textColor="@color/white"
                android:textSize="@dimen/ts_xl" />

            <EditText
                android:id="@+id/et_floor"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="2"
                android:hint="@string/text_please_input_floor"
                android:textColor="@color/white"
                android:inputType="number"
                android:textSize="@dimen/ts_xl" />

            <Button
                android:id="@+id/btn_delete"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/ts_common"
                android:layout_marginEnd="@dimen/ts_common"
                android:layout_weight="1"
                android:background="@drawable/bg_common_button_red"
                android:text="@string/text_delete_port"
                android:textAllCaps="false"
                android:textColor="@android:color/white"
                android:textSize="@dimen/ts_xl" />

        </LinearLayout>

</LinearLayout>
<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/white"
    android:padding="40dp">

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:gravity="center"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_wechat"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="25dp"
            android:gravity="center"
            android:text="@string/text_open_wechat"
            android:textColor="@android:color/black"
            android:textSize="30sp" />

        <ImageView
            android:layout_width="250dp"
            android:layout_height="250dp"
            android:src="@drawable/img_wechat_app" />


    </LinearLayout>

    <View
        android:layout_width="1dp"
        android:layout_height="match_parent"
        android:background="#33000000" />


    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:gravity="center"
        android:orientation="vertical">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="25dp"
            android:gravity="center"
            android:text="@string/text_scan_code_to_bind_machine"
            android:textColor="@android:color/black"
            android:textSize="30sp" />

        <ImageView
            android:id="@+id/iv_qrcode"
            android:layout_width="250dp"
            android:layout_height="250dp" />

    </LinearLayout>
</LinearLayout>
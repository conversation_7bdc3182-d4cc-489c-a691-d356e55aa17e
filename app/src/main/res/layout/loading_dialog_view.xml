<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/dialog_view"
    android:layout_width="fill_parent"
    android:layout_height="fill_parent"
    android:gravity="center"
    android:minWidth="190dp"
    android:minHeight="100dp"
    android:orientation="vertical"
    android:padding="60dp">

    <com.github.ybq.android.spinkit.SpinKitView
        android:id="@+id/stv_img"
        style="@style/SpinKitView.Large.FadingCircle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        app:SpinKit_Color="#0066aa" />


    <TextView
        android:id="@+id/tv_loading_prompt"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/mr_xl"
        android:textColor="#999"
        android:textSize="@dimen/ts_md" />

</LinearLayout>
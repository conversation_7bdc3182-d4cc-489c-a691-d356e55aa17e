<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#071949">

    <LinearLayout
        android:layout_width="460dp"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:background="@drawable/bg_round_card"
        android:gravity="center"
        android:orientation="vertical"
        android:padding="@dimen/pd_lg">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:paddingTop="10dp"
            android:paddingBottom="10dp"
            android:text="@string/voice_please_login"
            android:textColor="#2C8CEF"
            android:textSize="28sp"
            android:textStyle="bold" />


        <EditText
            android:id="@+id/et_username"
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:backgroundTint="#1B94EF"
            android:hint="@string/text_please_enter_username"
            android:inputType="textNoSuggestions"
            android:textColorHint="#aaa"
            android:textSize="@dimen/ts_common" />


        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:passwordToggleEnabled="true">

            <EditText
                android:id="@+id/et_password"
                android:layout_width="match_parent"
                android:layout_height="60dp"
                android:layout_marginTop="@dimen/mr_md"
                android:backgroundTint="#1B94EF"
                android:hint="@string/text_please_enter_password"
                android:inputType="textPassword"
                android:textColorHint="#aaa"
                android:textSize="@dimen/ts_common" />
        </com.google.android.material.textfield.TextInputLayout>


        <Button
            android:id="@+id/btn_login"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:gravity="center"
            android:paddingTop="@dimen/pd_md"
            android:paddingBottom="@dimen/pd_md"
            android:text="@string/text_login"
            android:textAllCaps="false"
            android:textColor="@color/white"
            android:textSize="20sp"
            android:textStyle="bold"
            app:backgroundTint="#1B94EF" />

        <Button
            android:id="@+id/btn_skip"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/mr_xs"
            android:gravity="center"
            android:paddingTop="@dimen/pd_sm"
            android:paddingBottom="@dimen/pd_md"
            android:text="@string/text_skip"
            android:textAllCaps="false"
            android:textColor="@color/white"
            android:textSize="20sp"
            android:textStyle="bold"
            app:backgroundTint="@android:color/darker_gray" />

    </LinearLayout>

</RelativeLayout>
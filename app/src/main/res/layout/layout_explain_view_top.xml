<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="bottom|center_horizontal"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tv_explain"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/content_bg"
        android:gravity="center"
        android:padding="5dp"
        android:singleLine="false"
        android:textSize="@dimen/ts_common" />

    <View
        android:layout_width="10dp"
        android:layout_height="10dp"
        android:background="@drawable/ic_arrow_bg" />

    <View
        android:id="@+id/arrow_line"
        android:layout_width="2dp"
        android:layout_height="30dp"
        android:background="#E8E8E8"
        android:rotation="180" />
</LinearLayout>
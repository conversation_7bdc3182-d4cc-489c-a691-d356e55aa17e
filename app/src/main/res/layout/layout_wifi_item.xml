<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="@dimen/mr_md"
    android:layout_marginEnd="@dimen/mr_md"
    android:gravity="center_vertical"
    android:paddingTop="@dimen/pd_md"
    android:paddingBottom="@dimen/pd_md">


    <TextView
        android:id="@+id/tv_wifi_ssid"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentStart="true"
        android:textColor="#efefef"
        android:textSize="@dimen/ts_common" />


    <ImageView
        android:id="@+id/iv_wifi_strength"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true" />
</RelativeLayout>
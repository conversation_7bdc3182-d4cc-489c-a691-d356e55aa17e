<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#071949">

    <LinearLayout
        android:layout_width="600dp"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:background="@drawable/bg_round_card"
        android:gravity="center"
        android:orientation="vertical"
        android:padding="@dimen/pd_md">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:paddingTop="10dp"
            android:paddingBottom="10dp"
            android:text="@string/text_robot_type_choose"
            android:textColor="#2C8CEF"
            android:textSize="36sp"
            android:textStyle="bold" />

        <RadioGroup
            android:id="@+id/rg_robot_type_control"
            android:layout_width="500dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="10dp"
            android:paddingTop="@dimen/pd_md"
            android:paddingBottom="@dimen/pd_md">

            <RadioButton
                android:id="@+id/rb_atomization_disinfection_robot"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="30dp"
                android:layout_marginBottom="10dp"
                android:button="@drawable/selector_radio_button_select"
                android:checked="true"
                android:paddingStart="8dp"
                android:text="@string/text_atomization_disinfection_robot"
                android:textSize="28sp" />


            <RadioButton
                android:id="@+id/rb_ultraviolet_disinfection_robot"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:layout_marginRight="30dp"
                android:button="@drawable/selector_radio_button_select"
                android:paddingStart="8dp"
                android:text="@string/text_ultraviolet_disinfection_robot"
                android:textSize="28sp" />

        </RadioGroup>

        <Button
            android:id="@+id/btn_confirm"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="10dp"
            android:layout_marginRight="20dp"
            android:gravity="center"
            android:paddingTop="@dimen/pd_md"
            android:paddingBottom="@dimen/pd_md"
            android:text="@string/text_save"
            android:textAllCaps="false"
            android:textAppearance="?android:attr/textAppearanceButton"
            android:textColor="@color/white"
            android:textSize="20sp"
            android:textStyle="bold"
            app:backgroundTint="#1B94EF" />

    </LinearLayout>

</RelativeLayout>
<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center"
    android:orientation="vertical"
    android:paddingLeft="@dimen/pd_lg"
    android:paddingRight="@dimen/pd_lg">

    <TextView
        android:id="@+id/tv_spinner_item"
        android:layout_width="match_parent"
        android:layout_height="?listPreferredItemHeight"
        android:layout_gravity="center_vertical"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:singleLine="true"
        android:textSize="@dimen/ts_common" />

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/color_split_line" />

</LinearLayout>
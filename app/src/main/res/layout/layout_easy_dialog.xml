<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:minWidth="300dp"
    android:paddingLeft="20dp"
    android:paddingTop="15dp"
    android:paddingRight="20dp"
    android:paddingBottom="20dp">

    <TextView
        android:id="@+id/tv_dialog_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/text_dialog_title_prompt"
        android:textColor="#333"
        android:textSize="26sp" />

    <LinearLayout
        android:id="@+id/fl_dialog_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/tv_dialog_title"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="20dp"
        android:gravity="center"
        android:minHeight="80dp"
        android:padding="10dp">

        <TextView
            android:id="@+id/tv_content"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:lineSpacingExtra="6dp"
            android:textColor="#666"
            android:textSize="24sp" />

    </LinearLayout>


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/fl_dialog_content"
        android:gravity="end">

        <Button
            android:id="@+id/btn_cancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="20dp"
            android:background="@drawable/bg_common_button_inactive"
            android:text="@string/text_cancel"
            android:textAllCaps="false"
            android:textColor="@android:color/white"
            android:textSize="22sp" />

        <Button
            android:id="@+id/btn_neutral"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="20dp"
            android:background="@drawable/bg_common_button_inactive"
            android:text="@string/text_cancel"
            android:textAllCaps="false"
            android:textColor="@android:color/white"
            android:textSize="22sp" />

        <Button
            android:id="@+id/btn_confirm"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_common_button_pressed"
            android:text="@string/text_confirm"
            android:textAllCaps="false"
            android:textColor="@android:color/white"
            android:textSize="22sp" />

    </LinearLayout>

</RelativeLayout>
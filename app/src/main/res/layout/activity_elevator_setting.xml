<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_main_background"
    android:orientation="vertical"
    android:paddingLeft="40dp"
    android:paddingTop="@dimen/pd_sm"
    android:paddingRight="40dp"
    android:paddingBottom="@dimen/pd_xl"
    tools:context=".activities.ElevatorSettingActivity">

    <RelativeLayout
        android:id="@+id/rl_top"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/tv_back"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawableStart="@drawable/ic_back"
            android:padding="@dimen/pd_lg"
            android:text="@string/text_back"
            android:textColor="@android:color/white"
            android:textSize="@dimen/ts_xl" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:text="@string/text_elevator_setting"
            android:textColor="@android:color/white"
            android:textSize="@dimen/ts_common"
            android:textStyle="bold" />


    </RelativeLayout>

    <ScrollView
        android:id="@+id/sv_content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/img_content_background"
        android:overScrollMode="never"
        android:padding="@dimen/pd_xl"
        android:scrollbars="none">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:orientation="vertical">


            <LinearLayout
                android:id="@+id/ll_wifi"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:paddingTop="@dimen/pd_sm"
                android:paddingBottom="@dimen/pd_sm"
                android:visibility="gone">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/text_choose_wifi"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_common" />

                <TextView
                    android:id="@+id/tv_wifi_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/pd_lg"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_common" />

                <Button
                    android:id="@+id/btn_wifi_choose"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/pd_lg"
                    android:background="@drawable/selector_common_button"
                    android:text="@string/text_choose"
                    android:textColor="@color/white"
                    android:textSize="@dimen/ts_common" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:paddingTop="@dimen/pd_sm"
                android:paddingBottom="@dimen/pd_sm"
                android:visibility="gone">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/text_main_control_ip"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_common" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:descendantFocusability="beforeDescendants"
                    android:focusable="true"
                    android:focusableInTouchMode="true">

                    <androidx.appcompat.widget.AppCompatEditText
                        android:id="@+id/et_main_ip1"
                        android:layout_width="60dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/mr_lg"
                        android:inputType="number"
                        android:maxLength="3"
                        android:scrollHorizontally="true"
                        android:singleLine="true"
                        android:textAlignment="center"
                        android:textColor="@android:color/white"
                        android:textColorHint="@color/white"
                        android:textSize="@dimen/ts_common"
                        android:theme="@style/EditTextStyle" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/mr_xs"
                        android:layout_marginEnd="@dimen/mr_xs"
                        android:text="."
                        android:textColor="@color/white"
                        android:textSize="@dimen/ts_common"
                        android:textStyle="bold" />

                    <EditText
                        android:id="@+id/et_main_ip2"
                        android:layout_width="60dp"
                        android:layout_height="wrap_content"
                        android:inputType="number"
                        android:maxLength="3"
                        android:scrollHorizontally="true"
                        android:singleLine="true"
                        android:textAlignment="center"
                        android:textColor="@android:color/white"
                        android:textColorHint="@color/white"
                        android:textSize="@dimen/ts_common"
                        android:theme="@style/EditTextStyle" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/mr_xs"
                        android:layout_marginEnd="@dimen/mr_xs"
                        android:text="."
                        android:textColor="@color/white"
                        android:textSize="@dimen/ts_common"
                        android:textStyle="bold" />

                    <EditText
                        android:id="@+id/et_main_ip3"
                        android:layout_width="60dp"
                        android:layout_height="wrap_content"
                        android:inputType="number"
                        android:maxLength="3"
                        android:scrollHorizontally="true"
                        android:singleLine="true"
                        android:textAlignment="center"
                        android:textColor="@android:color/white"
                        android:textColorHint="@color/white"
                        android:textSize="@dimen/ts_common"
                        android:theme="@style/EditTextStyle" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/mr_xs"
                        android:layout_marginEnd="@dimen/mr_xs"
                        android:text="."
                        android:textColor="@color/white"
                        android:textSize="@dimen/ts_common"
                        android:textStyle="bold" />

                    <EditText
                        android:id="@+id/et_main_ip4"
                        android:layout_width="60dp"
                        android:layout_height="wrap_content"
                        android:inputType="number"
                        android:maxLength="3"
                        android:scrollHorizontally="true"
                        android:singleLine="true"
                        android:textAlignment="center"
                        android:textColor="@android:color/white"
                        android:textColorHint="@color/white"
                        android:textSize="@dimen/ts_common"
                        android:theme="@style/EditTextStyle" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/mr_xs"
                        android:layout_marginEnd="@dimen/mr_xs"
                        android:text=":"
                        android:textColor="@color/white"
                        android:textSize="@dimen/ts_common"
                        android:textStyle="bold" />

                    <EditText
                        android:id="@+id/et_main_port"
                        android:layout_width="100dp"
                        android:layout_height="wrap_content"
                        android:inputType="number"
                        android:maxLength="5"
                        android:scrollHorizontally="true"
                        android:singleLine="true"
                        android:textAlignment="center"
                        android:textColor="@android:color/white"
                        android:textColorHint="@color/white"
                        android:textSize="@dimen/ts_common"
                        android:theme="@style/EditTextStyle" />
                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:paddingTop="@dimen/pd_sm"
                android:paddingBottom="@dimen/pd_sm"
                android:visibility="gone">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/text_out_control_ip"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_common" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:descendantFocusability="beforeDescendants"
                    android:focusable="true"
                    android:focusableInTouchMode="true">

                    <EditText
                        android:id="@+id/et_out_ip1"
                        android:layout_width="60dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/mr_lg"
                        android:inputType="number"
                        android:maxLength="3"
                        android:scrollHorizontally="true"
                        android:singleLine="true"
                        android:textAlignment="center"
                        android:textColor="@android:color/white"
                        android:textColorHint="@color/white"
                        android:textSize="@dimen/ts_common"
                        android:theme="@style/EditTextStyle" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/mr_xs"
                        android:layout_marginEnd="@dimen/mr_xs"
                        android:text="."
                        android:textColor="@color/white"
                        android:textSize="@dimen/ts_common"
                        android:textStyle="bold" />

                    <EditText
                        android:id="@+id/et_out_ip2"
                        android:layout_width="60dp"
                        android:layout_height="wrap_content"
                        android:inputType="number"
                        android:maxLength="3"
                        android:scrollHorizontally="true"
                        android:singleLine="true"
                        android:textAlignment="center"
                        android:textColor="@android:color/white"
                        android:textColorHint="@color/white"
                        android:textSize="@dimen/ts_common"
                        android:theme="@style/EditTextStyle" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/mr_xs"
                        android:layout_marginEnd="@dimen/mr_xs"
                        android:text="."
                        android:textColor="@color/white"
                        android:textSize="@dimen/ts_common"
                        android:textStyle="bold" />

                    <EditText
                        android:id="@+id/et_out_ip3"
                        android:layout_width="60dp"
                        android:layout_height="wrap_content"
                        android:inputType="number"
                        android:maxLength="3"
                        android:scrollHorizontally="true"
                        android:singleLine="true"
                        android:textAlignment="center"
                        android:textColor="@android:color/white"
                        android:textColorHint="@color/white"
                        android:textSize="@dimen/ts_common"
                        android:theme="@style/EditTextStyle" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/mr_xs"
                        android:layout_marginEnd="@dimen/mr_xs"
                        android:text="."
                        android:textColor="@color/white"
                        android:textSize="@dimen/ts_common"
                        android:textStyle="bold" />

                    <EditText
                        android:id="@+id/et_out_ip4"
                        android:layout_width="60dp"
                        android:layout_height="wrap_content"
                        android:inputType="number"
                        android:maxLength="3"
                        android:scrollHorizontally="true"
                        android:singleLine="true"
                        android:textAlignment="center"
                        android:textColor="@android:color/white"
                        android:textColorHint="@color/white"
                        android:textSize="@dimen/ts_common"
                        android:theme="@style/EditTextStyle" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/mr_xs"
                        android:layout_marginEnd="@dimen/mr_xs"
                        android:text=":"
                        android:textColor="@color/white"
                        android:textSize="@dimen/ts_common"
                        android:textStyle="bold" />

                    <EditText
                        android:id="@+id/et_out_port"
                        android:layout_width="100dp"
                        android:layout_height="wrap_content"
                        android:inputType="number"
                        android:maxLength="5"
                        android:scrollHorizontally="true"
                        android:singleLine="true"
                        android:textAlignment="center"
                        android:textColor="@android:color/white"
                        android:textColorHint="@color/white"
                        android:textSize="@dimen/ts_common"
                        android:theme="@style/EditTextStyle" />
                </LinearLayout>

            </LinearLayout>

            <LinearLayout
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:paddingTop="@dimen/pd_sm"
                android:paddingBottom="@dimen/pd_sm">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/text_port_map"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_common" />

                <Button
                    android:id="@+id/btn_port_choose"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/pd_lg"
                    android:background="@drawable/selector_common_button"
                    android:text="@string/text_choose"
                    android:textColor="@color/white"
                    android:textSize="@dimen/ts_common" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:paddingTop="@dimen/pd_sm"
                android:paddingBottom="@dimen/pd_sm">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/text_default_map"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_common" />

                <TextView
                    android:id="@+id/tv_default_map"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/pd_lg"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_common" />

                <Button
                    android:id="@+id/btn_choose"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/pd_lg"
                    android:background="@drawable/selector_common_button"
                    android:text="@string/text_choose"
                    android:textColor="@color/white"
                    android:textSize="@dimen/ts_common" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:paddingTop="@dimen/pd_sm"
                android:paddingBottom="@dimen/pd_sm">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/text_floor_level_tip"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/ts_common" />

                <RadioGroup
                    android:id="@+id/rg_floor_level"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/mr_lg"
                    android:orientation="horizontal"
                    >
                    <RadioButton
                        android:id="@+id/rb_floor_level_low"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:buttonTint="@android:color/white"
                        android:checked="true"
                        android:text="@string/text_floor_level_low"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/ts_common"

                        />

                    <RadioButton
                        android:id="@+id/rb_floor_level_high"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:buttonTint="@android:color/white"
                        android:layout_marginStart="@dimen/mr_md"
                        android:text="@string/text_floor_level_high"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/ts_common"

                        />
                </RadioGroup>
            </LinearLayout>


        </LinearLayout>
    </ScrollView>

</LinearLayout>
<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/white"
    tools:context=".activities.MapBuildingActivity">


    <FrameLayout
        android:id="@+id/web_layout"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1.4" />

    <RelativeLayout
        android:id="@+id/ll_operation_bar"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:orientation="vertical"
        android:visibility="invisible">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_above="@id/ll_nav_info"
            android:layout_alignParentTop="true"
            android:layout_centerHorizontal="true"
            android:background="#ffffff"
            android:clickable="true"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:gravity="center"
            android:orientation="vertical"
            android:paddingLeft="40dp"
            android:paddingTop="15dp"
            android:paddingRight="40dp"
            android:paddingBottom="15dp">

            <TextView
                android:id="@+id/tv_rebuild_map"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/mr_md"
                android:layout_marginEnd="@dimen/mr_md"
                android:layout_marginBottom="30dp"
                android:background="@drawable/bg_common_button_pressed"
                android:gravity="center"
                android:paddingLeft="@dimen/pd_md"
                android:paddingTop="@dimen/pd_md"
                android:paddingRight="@dimen/pd_md"
                android:paddingBottom="@dimen/pd_md"
                android:text="@string/text_rebuild_map"
                android:textColor="@android:color/white"
                android:textSize="@dimen/ts_lg" />


            <TextView
                android:id="@+id/tv_set_disinfection_point"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/mr_md"
                android:layout_marginEnd="@dimen/mr_md"
                android:layout_marginBottom="30dp"
                android:background="@drawable/bg_common_button_pressed"
                android:gravity="center"
                android:paddingLeft="@dimen/pd_md"
                android:paddingTop="@dimen/pd_md"
                android:paddingRight="@dimen/pd_md"
                android:paddingBottom="@dimen/pd_md"
                android:text="@string/text_set_disinfection_point"
                android:textColor="@android:color/white"
                android:textSize="@dimen/ts_lg" />

            <TextView
                android:id="@+id/tv_set_charging_pile"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/mr_md"
                android:layout_marginEnd="@dimen/mr_md"
                android:layout_marginBottom="30dp"
                android:background="@drawable/bg_common_button_pressed"
                android:gravity="center"
                android:paddingLeft="@dimen/pd_md"
                android:paddingTop="@dimen/pd_md"
                android:paddingRight="@dimen/pd_md"
                android:paddingBottom="@dimen/pd_md"
                android:text="@string/text_set_charging_pile"
                android:textColor="@android:color/white"
                android:textSize="@dimen/ts_lg" />

            <TextView
                android:id="@+id/tv_disinfection_route_test"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/mr_md"
                android:layout_marginEnd="@dimen/mr_md"
                android:layout_marginBottom="30dp"
                android:background="@drawable/bg_common_button_pressed"
                android:gravity="center"
                android:paddingLeft="@dimen/pd_md"
                android:paddingTop="@dimen/pd_md"
                android:paddingRight="@dimen/pd_md"
                android:paddingBottom="@dimen/pd_md"
                android:text="@string/text_disinfection_point_test"
                android:textColor="@android:color/white"
                android:textSize="@dimen/ts_lg" />


            <TextView
                android:id="@+id/tv_save_map"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/mr_md"
                android:layout_marginEnd="@dimen/mr_md"
                android:layout_marginBottom="30dp"
                android:background="@drawable/bg_common_button_pressed"
                android:gravity="center"
                android:paddingLeft="@dimen/pd_md"
                android:paddingTop="@dimen/pd_md"
                android:paddingRight="@dimen/pd_md"
                android:paddingBottom="@dimen/pd_md"
                android:text="@string/text_save_map"
                android:textColor="@android:color/white"
                android:textSize="@dimen/ts_lg" />

            <TextView
                android:id="@+id/tv_back_to_main_page"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/mr_md"
                android:layout_marginEnd="@dimen/mr_md"
                android:layout_marginBottom="30dp"
                android:background="@drawable/bg_common_button_pressed"
                android:gravity="center"
                android:paddingLeft="@dimen/pd_md"
                android:paddingTop="@dimen/pd_md"
                android:paddingRight="@dimen/pd_md"
                android:paddingBottom="@dimen/pd_md"
                android:text="@string/text_back_to_main_page"
                android:textColor="@android:color/white"
                android:textSize="@dimen/ts_lg" />

            <TextView
                android:id="@+id/tv_exit_deploy"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/mr_md"
                android:layout_marginEnd="@dimen/mr_md"
                android:layout_marginBottom="30dp"
                android:background="@drawable/bg_common_button_pressed"
                android:gravity="center"
                android:paddingLeft="@dimen/pd_md"
                android:paddingTop="@dimen/pd_md"
                android:paddingRight="@dimen/pd_md"
                android:paddingBottom="@dimen/pd_md"
                android:text="@string/text_exit_deploy"
                android:textColor="@android:color/white"
                android:textSize="@dimen/ts_lg" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_nav_info"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingStart="60dp"
            android:layout_alignParentBottom="true"
            android:layout_marginBottom="@dimen/mr_sm"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_host_ip"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/mr_sm"
                android:ellipsize="end"
                android:singleLine="true"
                android:textColor="#aaa"
                android:textSize="@dimen/ts_md" />

            <TextView
                android:id="@+id/tv_wifi"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/mr_sm"
                android:ellipsize="end"
                android:singleLine="true"
                android:textColor="#aaa"
                android:textSize="@dimen/ts_md" />

            <TextView
                android:id="@+id/tv_host_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:singleLine="true"
                android:textColor="#aaa"
                android:textSize="@dimen/ts_md" />
        </LinearLayout>
    </RelativeLayout>

</LinearLayout>
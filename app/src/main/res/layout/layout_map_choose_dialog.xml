<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="@dimen/pd_md">

    <TextView
        android:id="@+id/tv_dialog_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:text="@string/text_please_choose_map"
        android:textColor="#2C8CEF"
        android:textSize="@dimen/ts_common"
        android:textStyle="bold" />

    <androidx.core.widget.NestedScrollView
        android:id="@+id/sr_map_list"
        android:layout_width="match_parent"
        android:layout_height="300dp"
        android:layout_below="@id/tv_dialog_title"
        android:layout_marginTop="@dimen/mr_md">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/lv_map_list"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />
    </androidx.core.widget.NestedScrollView>

    <Button
        android:id="@+id/btn_confirm"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/sr_map_list"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="@dimen/mr_md"
        android:background="@drawable/selector_common_button"
        android:paddingTop="10dp"
        android:paddingBottom="10dp"
        android:text="@string/text_confirm"
        android:textAllCaps="false"
        android:textColor="@android:color/white"
        android:textSize="@dimen/ts_common" />

</RelativeLayout>
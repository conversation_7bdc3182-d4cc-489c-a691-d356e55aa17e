<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="@dimen/pd_md">

    <EditText
        android:id="@+id/et_unlock_password"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:enabled="false"
        android:gravity="center"
        android:hint="@string/text_please_enter_lock_password"
        android:inputType="textPassword"
        android:textSize="@dimen/ts_lg" />

    <GridLayout
        android:id="@+id/gl_lock_screen_keys"
        android:layout_width="500dp"
        android:layout_height="wrap_content"
        android:columnCount="3"
        android:rowCount="4">

        <TextView
            android:layout_width="166dp"
            android:layout_height="wrap_content"
            android:background="@drawable/outline_lock_screen_key"
            android:gravity="center"
            android:paddingTop="@dimen/pd_sm"
            android:paddingBottom="@dimen/pd_sm"
            android:text="1"
            android:textSize="@dimen/ts_common" />

        <TextView
            android:layout_width="166dp"
            android:layout_height="wrap_content"
            android:background="@drawable/outline_lock_screen_key"
            android:gravity="center"
            android:paddingTop="@dimen/pd_sm"
            android:paddingBottom="@dimen/pd_sm"
            android:text="2"
            android:textSize="@dimen/ts_common" />

        <TextView
            android:layout_width="166dp"
            android:layout_height="wrap_content"
            android:background="@drawable/outline_lock_screen_key"
            android:gravity="center"
            android:paddingTop="@dimen/pd_sm"
            android:paddingBottom="@dimen/pd_sm"
            android:text="3"
            android:textSize="@dimen/ts_common" />

        <TextView
            android:layout_width="166dp"
            android:layout_height="wrap_content"
            android:background="@drawable/outline_lock_screen_key"
            android:gravity="center"
            android:paddingTop="@dimen/pd_sm"
            android:paddingBottom="@dimen/pd_sm"
            android:text="4"
            android:textSize="@dimen/ts_common" />

        <TextView
            android:layout_width="166dp"
            android:layout_height="wrap_content"
            android:background="@drawable/outline_lock_screen_key"
            android:gravity="center"
            android:paddingTop="@dimen/pd_sm"
            android:paddingBottom="@dimen/pd_sm"
            android:text="5"
            android:textSize="@dimen/ts_common" />

        <TextView
            android:layout_width="166dp"
            android:layout_height="wrap_content"
            android:background="@drawable/outline_lock_screen_key"
            android:gravity="center"
            android:paddingTop="@dimen/pd_sm"
            android:paddingBottom="@dimen/pd_sm"
            android:text="6"
            android:textSize="@dimen/ts_common" />

        <TextView
            android:layout_width="166dp"
            android:layout_height="wrap_content"
            android:background="@drawable/outline_lock_screen_key"
            android:gravity="center"
            android:paddingTop="@dimen/pd_sm"
            android:paddingBottom="@dimen/pd_sm"
            android:text="7"
            android:textSize="@dimen/ts_common" />

        <TextView
            android:layout_width="166dp"
            android:layout_height="wrap_content"
            android:background="@drawable/outline_lock_screen_key"
            android:gravity="center"
            android:paddingTop="@dimen/pd_sm"
            android:paddingBottom="@dimen/pd_sm"
            android:text="8"
            android:textSize="@dimen/ts_common" />

        <TextView
            android:layout_width="166dp"
            android:layout_height="wrap_content"
            android:background="@drawable/outline_lock_screen_key"
            android:gravity="center"
            android:paddingTop="@dimen/pd_sm"
            android:paddingBottom="@dimen/pd_sm"
            android:text="9"
            android:textSize="@dimen/ts_common" />

        <TextView
            android:layout_width="166dp"
            android:layout_height="wrap_content"
            android:background="@drawable/outline_lock_screen_key"
            android:gravity="center"
            android:paddingTop="@dimen/pd_sm"
            android:paddingBottom="@dimen/pd_sm"
            android:text="0"
            android:textSize="@dimen/ts_common" />

        <TextView
            android:id="@+id/tv_clear"
            android:layout_width="166dp"
            android:layout_height="wrap_content"
            android:background="@drawable/outline_lock_screen_key"
            android:gravity="center"
            android:paddingTop="@dimen/pd_sm"
            android:paddingBottom="@dimen/pd_sm"
            android:text="x"
            android:textSize="@dimen/ts_common" />

        <TextView
            android:id="@+id/tv_delete"
            android:layout_width="166dp"
            android:layout_height="wrap_content"
            android:background="@drawable/outline_lock_screen_key"
            android:gravity="center"
            android:paddingTop="@dimen/pd_sm"
            android:paddingBottom="@dimen/pd_sm"
            android:text="@string/text_delete"
            android:textSize="@dimen/ts_common" />
    </GridLayout>

    <Button
        android:id="@+id/btn_confirm"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginTop="@dimen/mr_md"
        android:background="@drawable/btn_bg"
        android:text="@string/text_confirm"
        android:textColor="@android:color/white"
        android:textSize="@dimen/ts_common" />

</LinearLayout>
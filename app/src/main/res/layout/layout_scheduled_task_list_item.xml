<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="@dimen/mr_xs"
    android:layout_marginBottom="@dimen/mr_xs"
    android:background="@drawable/bg_scheduled_task_list_item"
    android:paddingTop="@dimen/pd_sm"
    android:paddingBottom="@dimen/pd_sm">

    <TextView
        android:id="@+id/tv_task_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginStart="@dimen/mr_lg"
        android:drawableStart="@drawable/icon_list_item_task_name"
        android:drawablePadding="@dimen/pd_sm"
        android:ellipsize="end"
        android:gravity="center"
        android:maxWidth="500dp"
        android:singleLine="true"
        android:textColor="@android:color/white"
        android:textSize="@dimen/ts_common" />


    <TextView
        android:id="@+id/tv_task_start_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:layout_marginEnd="@dimen/mr_xl"
        android:layout_toStartOf="@id/sw_task_enable"
        android:drawableStart="@drawable/icon_list_item_task_time"
        android:drawablePadding="@dimen/pd_sm"
        android:ellipsize="end"
        android:gravity="center"
        android:maxWidth="200dp"
        android:singleLine="true"
        android:textColor="@android:color/white"
        android:textSize="@dimen/ts_common" />


    <androidx.appcompat.widget.SwitchCompat
        android:id="@+id/sw_task_enable"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:layout_centerHorizontal="true"
        android:layout_centerVertical="true"
        android:layout_marginEnd="40dp"
        android:padding="@dimen/pd_md"
        android:thumb="@drawable/switch_ios_thumb"
        app:track="@drawable/switch_selector" />


</RelativeLayout>
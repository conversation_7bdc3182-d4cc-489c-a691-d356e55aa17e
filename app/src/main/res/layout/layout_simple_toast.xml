<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/shape_simple_toast">

    <TextView
        android:id="@+id/tv_toast_content"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingLeft="@dimen/pd_sm"
        android:paddingTop="@dimen/pd_xs"
        android:paddingRight="@dimen/pd_sm"
        android:paddingBottom="@dimen/pd_xs"
        android:textColor="@android:color/white"
        android:textSize="@dimen/ts_lg" />

</FrameLayout>
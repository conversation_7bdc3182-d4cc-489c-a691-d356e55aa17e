<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="strNetworkTipsCancelBtn">キャンセル</string>
    <string name="strNetworkTipsConfirmBtn">ダウンロードを続けます</string>
    <string name="strNetworkTipsMessage">モバイルネットワークに切り替えましたが、現在のダウンロードを続けますか？</string>
    <string name="strNetworkTipsTitle">ネットワークのヒント</string>
    <string name="strNotificationClickToContinue">ダウンロードを続けます</string>
    <string name="strNotificationClickToInstall">インストールをクリックします</string>
    <string name="strNotificationClickToRetry">クリックしてやり直します</string>
    <string name="strNotificationClickToView">クリックして</string>
    <string name="strNotificationDownloadError">ダウンロード失敗</string>
    <string name="strNotificationDownloadSucc">ダウンロード完了</string>
    <string name="strNotificationDownloading">ダウンロード中</string>
    <string name="strNotificationHaveNewVersion">新しいバージョンがあります</string>
    <string name="strToastCheckUpgradeError">新しいバージョンのチェックに失敗しました。後でやり直してください。</string>
    <string name="strToastCheckingUpgrade">検査中です。少々お待ちください。</string>
    <string name="strToastYourAreTheLatestVersion">あなたはもう最新版です。</string>
    <string name="strUpgradeDialogCancelBtn">また今度</string>
    <string name="strUpgradeDialogContinueBtn">続行</string>
    <string name="strUpgradeDialogFeatureLabel">説明を更新します</string>
    <string name="strUpgradeDialogFileSizeLabel">パッケージサイズ</string>
    <string name="strUpgradeDialogInstallBtn">インストール</string>
    <string name="strUpgradeDialogRetryBtn">再試行</string>
    <string name="strUpgradeDialogUpdateTimeLabel">更新日時</string>
    <string name="strUpgradeDialogUpgradeBtn">すぐに更新します</string>
    <string name="strUpgradeDialogVersionLabel">バージョン</string>

    <string name="pickerview_cancel">キャンセル</string>
    <string name="pickerview_submit">確認</string>

    <string name="exception_remote_cancel_navigation">現在のジョブはキャンセルされました。</string>
    <string name="exception_encounter_obstacle_in_the_way">ナビゲーション中に障害物が発生しました。ミッションが失敗しました。</string>
    <string name="exception_encounter_obstacle_in_target_point">目標点に障害物があり、ミッションが失敗しました。</string>
    <string name="exception_not_in_work_space">現在は業務エリア外にあり、任務は失敗しました。</string>
    <string name="exception_forbidden_zone">ペナルティエリアに入ると、タスクが失敗しました。</string>
    <string name="exception_tripped_in_obstacle">障害物に陥ったり、壁に近づきすぎたりして、ミッションが失敗しました。</string>
    <string name="exception_laser_error">レーザーは異常です。技術者に連絡してください。</string>
    <string name="exception_speedometer_error">マイルメーターが異常です。技術者に連絡してください。</string>
    <string name="exception_chassis_failure">シャーシが故障しました。技術者に連絡してください。</string>
    <string name="exception_imu_error">IMUの故障は、技術者に連絡してください。</string>
    <string name="exception_chassis_over_current">シャシーが流れますので、技術者に連絡してください。</string>
    <string name="exception_new_target">ナビゲーションの新しいターゲットを受信しましたが、タスクが失敗しました。</string>
    <string name="exception_encounter_obstacle_in_target_point_or_in_the_way">ナビゲーション途中や目標点に障害物があり、ナビゲーションに失敗しました。</string>

    <string name="text_app_name">DisinfectionRobot-Elevator</string>
    <string name="text_charging_pile">charging_pile</string>
    <string name="text_delete"><![CDATA[<]]></string>
    <string name="text_choose_map">地図を選択</string>
    <string name="text_title_recent_task">今日の最近のミッション</string>
    <string name="text_title_robot_state">ロボット状態</string>
    <string name="text_look_up_recent_task">最近のタスクの表示</string>
    <string name="text_go_to_charge">充電</string>
    <string name="text_title_manual_task">手動消毒モード</string>
    <string name="text_start_disinfection">消毒を開始します</string>
    <string name="text_lock_screen">ロック画面</string>
    <string name="text_choose_wifi">WiFi設定</string>
    <string name="text_build_map">ボットをデプロイする</string>
    <string name="text_title_task_setting">タスク設定</string>
    <string name="text_back">戻る</string>
    <string name="text_single_mode">単一実行</string>
    <string name="text_duration_mode">ループ実行</string>
    <string name="text_destination_when_task_complete">ミッション完了</string>
    <string name="text_go_to_starting_point_when_task_finished">出発点</string>
    <string name="text_disinfection_switch">消毒スイッチ</string>
    <string name="text_open_all_the_way">ずっと開く</string>
    <string name="text_open_in_target_point">ターゲットポイントで開く</string>
    <string name="text_close_all_the_way">常に閉じる</string>
    <string name="text_stay_time">目標地点での滞在期間</string>
    <string name="text_duration_time">ループ実行時間</string>
    <string name="text_cycle_mode">消毒モード</string>
    <string name="text_invalid_time_in_duration_mode">ループ実行時間が不正です（5秒以上）</string>
    <string name="text_open_in_target_point_when_stay_time_is_0">滞留時間が0の場合は、目標点消毒オプションを設定しないでください。</string>
    <string name="text_title_system_setting">システム設定</string>
    <string name="text_account">現在の使用者</string>
    <string name="text_not_login">ログインしていない</string>
    <string name="text_choice_voice_broadcast">消毒中の音声放送</string>
    <string name="text_open">開ける</string>
    <string name="text_close">無効</string>
    <string name="text_choice_lock_screen">画面設定をロックする</string>
    <string name="text_choice_speed">ロボットの歩行速度</string>
    <string name="text_low_power">自動充電</string>
    <string name="text_choice_volume">ボリュームコントロール</string>
    <string name="text_delay_disinfection">タスクカウントダウンの開始</string>
    <string name="text_second">秒</string>
    <string name="text_current_version">現在のバージョン番号</string>
    <string name="text_choose_speed">ロボットの走行速度(m/s)</string>
    <string name="text_invalid_low_power">自動充電設定が無効です（10〜80）</string>
    <string name="text_title_scheduled_task_list">スケジュールされたタスクのリスト</string>
    <string name="text_task_name">ミッション名</string>
    <string name="text_repeat_date">繰り返し日</string>
    <string name="text_repeat_monday">月曜</string>
    <string name="text_repeat_sunday">日曜</string>
    <string name="text_repeat_saturday">土曜</string>
    <string name="text_repeat_friday">金曜</string>
    <string name="text_repeat_thursday">木曜</string>
    <string name="text_repeat_wednesday">水曜</string>
    <string name="text_repeat_tuesday">火曜</string>
    <string name="text_start_time">開始時間</string>
    <string name="text_invalid_task_name">タスク名を空にすることはできません</string>
    <string name="text_create_disinfection_task">消毒タスクを作成する</string>
    <string name="text_modify_disinfection_task">消毒タスクを変更する</string>
    <string name="text_save">保存する</string>
    <string name="text_create">タスクを作成する</string>
    <string name="text_task_enabled">スケジュールされたタスクが有効になっている</string>
    <string name="text_task_disabled">スケジュールされたタスクは無効になっています</string>
    <string name="text_date_format">HH:mm:ss</string>
    <string name="text_invalid_repeat_time">営業日を選択してください</string>
    <string name="text_enter_lock_screen">ロック画面のパスワードを入力してください</string>
    <string name="text_cancel">キャンセル</string>
    <string name="text_confirm">もちろん</string>
    <string name="text_current_map">現在の地図</string>
    <string name="text_current_ip">ROS IP</string>
    <string name="text_current_wifi">ROS WiFi</string>
    <string name="text_default_scheduled_task_name">時限タスク%1$s</string>
    <string name="text_pause_task">タスクの一時停止</string>
    <string name="text_finish_task">タスクの終了</string>
    <string name="text_password_error">パスワードが間違っています。再入力してください</string>
    <string name="text_enter_lock_screen_password_again">パスワードの再入力</string>
    <string name="text_invalid_lock_screen_password">ロック画面のパスワードが無効です（4桁）</string>
    <string name="text_passwords_are_not_same">2つのパスワードが一致しません。再入力してください</string>
    <string name="text_rebuild_map">地図を再スキャン</string>
    <string name="text_set_disinfection_point">消毒ポイントを追加</string>
    <string name="text_set_charging_pile">充電パイルを設定する</string>
    <string name="text_exit_deploy">デプロイメントを終了します</string>
    <string name="text_save_map">マップを保存</string>
    <string name="text_back_to_main_page">メイン画面に戻る</string>
    <string name="text_disinfection_point_test">ルートテスト</string>
    <string name="text_dialog_title_prompt">ヒント</string>
    <string name="text_current_host_name">ROS名称: %1$s</string>
    <string name="text_emergency_stop_turn_on">非常停止スイッチがオン</string>
    <string name="text_charging">ロボットが充電中</string>
    <string name="text_low_pow">ロボットのバッテリーが少なくなっています</string>
    <string name="text_title_task_state">タスクステータス</string>
    <string name="text_current_task_time_remain">残り時間: %1$s</string>
    <string name="text_current_destination">目標: %1$s</string>
    <string name="text_current_task_type">タスクタイプ: %1$s</string>
    <string name="text_current_task_mode">ミッションモード: %1$s</string>
    <string name="text_current_task_start_time">始まる時間: %1$s</string>
    <string name="text_current_task_time_waste">経過時間: %1$s</string>
    <string name="text_current_disinfection_switch_state">消毒スイッチの状態: %1$s</string>
    <string name="text_test_result_charging_docking_success">充電とドッキングに成功</string>
    <string name="text_test_result_charging_docking_failed">充電とドッキングに失敗しました</string>
    <string name="text_route_test_success">消毒経路テストは成功しました</string>
    <string name="text_route_test_failed">消毒ルートテストが失敗し、消毒ポイント%1$sが正常に到着しませんでした</string>
    <string name="text_navigation_version">ROSバージョン番号</string>
    <string name="text_closed">閉まっている</string>
    <string name="text_opened">オープン</string>
    <string name="exception_visual_mark">位置決め異常</string>
    <string name="text_ros_wifi_connect_failed">ナビゲーションWiFi接続に失敗しました</string>
    <string name="text_android_wifi_connect_failed">Androidネットワーク接続に失敗しました</string>
    <string name="text_wifi_name_can_not_be_empty">WiFi名を空にすることはできません</string>
    <string name="text_speed_unit">m/s</string>
    <string name="text_please_choose_map">地図を選択してください</string>
    <string name="text_loading_map_list">地図を読み込んでいます、お待ちください...</string>
    <string name="text_finish">終了</string>
    <string name="text_please_enter_lock_password">ロック画面のパスワードを入力してください</string>
    <string name="text_exit_login">サインアウト</string>
    <string name="text_login">ログイン</string>
    <string name="text_login_success">ログイン成功</string>
    <string name="text_login_failed">ログインに失敗しました</string>
    <string name="text_username_can_not_be_empty">ユーザー名を空にすることはできません</string>
    <string name="text_please_enter_password">パスワード</string>
    <string name="text_skip">飛び越える</string>
    <string name="text_please_enter_username">ユーザー名</string>
    <string name="text_ultraviolet_disinfection_robot">紫外線消毒ロボット</string>
    <string name="text_atomization_disinfection_robot">スプレー消毒ロボット</string>
    <string name="text_robot_type_choose">モデルの選択</string>
    <string name="text_restart_for_configuration_change">アプリケーション構成が変更され、アプリケーションを再入力しようとしています</string>
    <string name="text_next_step">次のステップ</string>
    <string name="text_enter_app">プログラムに入る</string>
    <string name="text_exit_app">アプリを終了します</string>
    <string name="text_page_load_failed">ロードタイムアウトです。アンドロイドとナビゲーションホストが同じネットワークに接続されていることを確認してください。</string>
    <string name="text_ros_wifi">ROS WiFi: %1$s</string>
    <string name="text_ros_ip">ROS IP: %1$s</string>
    <string name="text_open_wechat">ステップ1:\n wechatスキャンの下のQRコードを開いてロボットアカウントにログインしてください</string>
    <string name="text_scan_code_to_bind_machine">ステップ2:\n wechatウィジェットを使用して下のQRコードをスキャンしてマシンをバインドしてください</string>
    <string name="text_wechat_app">アカウントのバインド</string>
    <string name="text_click_to_bind">ここをクリックしてアカウントをバインド</string>
    <string name="text_save_task">タスクの保存</string>
    <string name="text_switch">切り替え</string>
    <string name="text_invalid_delay_time">遅延消毒時間を入力してください</string>
    <string name="text_logout_success">アカウントを終了しました</string>
    <string name="text_start_right_now">今すぐ開始</string>
    <string name="text_cancel_task">タスクのキャンセル</string>
    <string name="text_do_not_apply_map_repeatedly">地図を繰り返し切り替えないでください</string>
    <string name="text_synthesize_error">オーディオの生成に失敗しました: %1$s</string>
    <string name="text_test_result_canceled_by_user">テストタスクは手動でキャンセルされました</string>
    <string name="text_test_result_success">消毒ポイントテストが成功し、充電ドッキングテストが成功しました</string>
    <string name="text_test_result_charger_not_found">充電とドッキングに失敗しました。充電パイルが見つかりません</string>
    <string name="text_test_failed_for_not_found_start_point">ルートテストに失敗しました。消毒の開始点が見つかりませんでした</string>
    <string name="text_going_to_charging_pile_for_charge_test">充電ドッキングテストのために充電パイルに行く</string>
    <string name="text_navigating_to_target_point_for_test">ルートテストのために消毒ポイント%1$sに向かう途中</string>
    <string name="text_going_to_start_task_in_future">%1$d秒でタスクを開始します</string>
    <string name="text_task_was_canceled">タスクはキャンセルされました</string>
    <string name="text_going_to_start_point">出発点に戻る</string>
    <string name="text_going_to_charge">充電ステーションに行って充電する</string>
    <string name="text_language_setting">現在の言語</string>
    <string name="text_task_already_update">タスクが更新されました</string>
    <string name="text_wifi_not_connect">未连接</string>
    <string name="text_map_load_failed">マップを読み込めませんでした</string>
    <string name="text_change_map_success">マップの切り替えに成功しました</string>
    <string name="text_changing_map">お待ちください</string>
    <string name="text_saving_task">お待ちください</string>
    <string name="text_is_login">お待ちください</string>
    <string name="text_task_saved_success">タスクの保存に成功しました</string>
    <string name="text_confirm_delete_this_task">現在スケジュールされているタスクを削除してもよろしいですか？</string>
    <string name="text_connect_time_out">接続がタイムアウトしました</string>
    <string name="text_connect_success">接続に成功しました</string>
    <string name="text_connect_failed">接続に失敗しました</string>
    <string name="text_going_to_charge_for_low_power">バッテリーが低すぎる場合、%1$d秒で充電するために充電パイルに行きます</string>
    <string name="text_going_to_exec_schedule_task">%1$d秒後にスケジュールされたタスクを開始します[%2$s]</string>
    <string name="text_wifi_password_can_not_be_empty">パスワードを空白にすることはできません</string>
    <string name="text_auth">ログイン</string>
    <string name="text_wifi_password">WiFiパスワード</string>
    <string name="text_wifi_name">WiFi名</string>
    <string name="text_wifi_auth">WiFi認証</string>
    <string name="text_already_updated">リフレッシュ</string>
    <string name="text_wifi_enabling">開いた</string>
    <string name="text_wifi_disabling">閉鎖</string>
    <string name="text_current_android_wifi">Android:%1$s</string>
    <string name="text_open_wifi_first">最初にWIFIをオンにしてください</string>
    <string name="text_current_ros_wifi">ROS:%1$s</string>
    <string name="text_scheduled_task">時限タスク</string>
    <string name="text_manual_task">手動タスク</string>
    <string name="text_enter_lock_screen_password">パスワードの入力</string>
    <string name="text_resume_task">回復タスク</string>
    <string name="text_can_not_find_starting_point">消毒開始点が見つかりません。消毒点がマークされているかどうかを確認してください</string>
    <string name="text_time_duration_format">%02d:%02d:%02d</string>
    <string name="text_time_duration_format2">%02d:%02d:%02d</string>
    <string name="text_confirm_add_disinfection_point_at_this_location">現在地をポイント%1$d消毒として設定してよろしいですか？</string>
    <string name="text_synthesize_audio">オーディション</string>
    <string name="text_save_success">保存に成功しました</string>
    <string name="text_disinfection_voice">消毒中の音声</string>
    <string name="text_not_found_audio_file">オーディオファイルが見つかりません。まずオーディオを生成してください。</string>
    <string name="text_input_disinfection_prompt_first">オーディオを合成する文字を入力してください</string>

    <string name="voice_low_power_go_to_charge">ロボットの電力が低すぎるので、充電しなければなりません</string>
    <string name="voice_task_finished">ミッション完了</string>
    <string name="voice_stop_charge">充電を停止します</string>
    <string name="voice_route_test_finished">消毒経路試験完了</string>
    <string name="voice_not_found_charging_pile">充電ステーションが見つかりません</string>
    <string name="voice_charging_dock_failed">充電に失敗しました</string>
    <string name="voice_charging_docking_failed_and_re_dock">充電ドッキングに失敗し、再ドッキングを試みています</string>
    <string name="voice_guide_goto_setting">ここをクリックして設定画面に入ります</string>
    <string name="voice_guide_more_actions">画面の右側から左にスライドすると、より多くの操作が可能になります。</string>
    <string name="voice_guide_finish">操作ガイド完了おめでとうございます!</string>
    <string name="voice_guide_goto_manual_task">手動タスクを設定するには、ここをクリックしてください</string>
    <string name="voice_guide_start_disinfection">ここをクリックして消毒タスクを開始します</string>
    <string name="voice_guide_look_up_recent_task">計画タスクを表示するには、ここをクリックします。</string>
    <string name="voice_guide_goto_charge">ここをクリックして充電指令を出してください</string>
    <string name="voice_guide_exit_app">連続して2回クリックした後にスクリーンの下から引いてアプリケーションを退出することができます</string>
    <string name="voice_not_guide_build_map">ロボットを使うのは初めてだということがわかりました。すぐに地図を作りましょう。</string>
    <string name="voice_going_to_charging">わかりました、充電に行きましょう</string>
    <string name="voice_already_in_charging">ロボットが充電中</string>
    <string name="voice_start_charging">充電を開始します</string>
    <string name="voice_start_docking_charging_pile">充電エリアに到着し、充電パイルのドッキングを開始します</string>
    <string name="voice_task_finish_and_go_to_start_point">ミッションが完了しました。出発点に戻ります</string>
    <string name="voice_click_to_rebuild_map">マップを再構築すると、現在スキャンされているマップが破棄され、その場所からマップが再スキャンされます。確認してください</string>
    <string name="voice_confirm_add_disinfection_point_at_this_location">現在地を消毒ポイントとして設定してもよろしいですか？</string>
    <string name="voice_confirm_set_charging_pile_at_this_location">ロボットが充電パイルに正しくドッキングされていることを確認してから、[OK]をクリックして現在の位置を充電パイルとして設定してください</string>
    <string name="voice_target_point_mark_failed">目標点の設定に失敗しました</string>
    <string name="voice_target_point_mark_success">ターゲットポイントが正常に設定されました</string>
    <string name="voice_confirm_exit_deploy">展開を終了してもよろしいですか？消毒ポイントと充電パイルのラベル付けが完了し、消毒ルートテストが正常に実行されたことを確認してください</string>
    <string name="voice_emergency_stop_turn_on">非常停止スイッチが押されており、移動できません</string>
    <string name="voice_entering_map_building_mode">スキャンマップモードに入ります、お待ちください...</string>
    <string name="voice_going_to_start_disinfection_route_test">消毒経路テストが開始され、消毒スイッチがオンになりません。消毒ポイントと充電パイルの位置のラベル付けが完了していることを確認してください。</string>
    <string name="voice_turn_off_scram_stop_to_start_task">タスクを開始する前に、非常停止スイッチをオフにしてください</string>
    <string name="voice_cut_off_power_to_start_task">タスクを開始する前に、電源アダプタを外してください</string>
    <string name="voice_going_to_start_disinfection_task">消毒ミッションを開始しようとしています</string>
    <string name="voice_stay_away_from_me">私は消毒任務にあります、私の近くに来ないでください</string>
    <string name="voice_task_finish_and_go_to_charge">タスクが完了したら、充電に戻ります</string>
    <string name="voice_saving_map_and_entering_nav_mode">地図を保存しています、お待ちください...</string>
    <string name="voice_click_to_save_map">マップを保存してマッピングモードを終了しようとしています。確認してください</string>
    <string name="voice_click_to_abandon_map_building">メインインターフェースに戻ると、現在スキャンされているマップは保存されません。操作を確認してください</string>
    <string name="voice_current_in_map_building_mode">現在、地図スキャンモードになっています。非常停止スイッチを押してから、ロボットを後ろから押して地図をスキャンしてください。</string>
    <string name="voice_current_in_nav_mode">現在ナビゲーションモードでは、消毒ポイントをマークして消毒ルートをテストできます</string>
    <string name="voice_entering_nav_mode">ナビゲーションモードに入るのを待ってください...</string>
    <string name="voice_wifi_connect_success">ネットワーク接続成功</string>
    <string name="voice_please_connect_wifi">ネットワーク接続</string>
    <string name="voice_please_login">ログイン</string>
    <string name="voice_select_robot_type">モデルの選択</string>
    <string name="voice_login_success">ログイン成功</string>
    <string name="voice_please_enter_app">どうぞお入りください</string>
    <string name="voice_connecting_wifi">ネットワークに接続しています。ちょっと待ってください。</string>

</resources>
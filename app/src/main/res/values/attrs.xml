<!--
  Copyright 2011 The Android Open Source Project

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->

<resources>

    <declare-styleable name="RippleBackground">
        <attr name="rb_color" format="color" />
        <attr name="rb_strokeWidth" format="dimension" />
        <attr name="rb_radius" format="dimension" />
        <attr name="rb_duration" format="integer" />
        <attr name="rb_rippleAmount" format="integer" />
        <attr name="rb_scale" format="float" />
        <attr name="rb_type" format="enum">
            <enum name="fillRipple" value="0" />
            <enum name="strokeRipple" value="1" />
        </attr>
    </declare-styleable>
</resources>
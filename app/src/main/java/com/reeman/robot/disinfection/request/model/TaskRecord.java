package com.reeman.robot.disinfection.request.model;

import com.google.gson.annotations.SerializedName;
import com.reeman.robot.disinfection.utils.BatteryUtils;

import java.util.Date;

import static com.reeman.robot.disinfection.base.BaseApplication.mApp;

public class TaskRecord {
    @SerializedName("title")
    private String title;
    @SerializedName("disinfectionMode")
    public int disinfectionMode;
    @SerializedName("taskStartTime")
    public long taskStartTime;
    @SerializedName("taskEndTime")
    public long taskEndTime;
    @SerializedName("taskStatus")
    public int taskStatus;
    @SerializedName("taskStartPower")
    public int taskStartPower;
    @SerializedName("taskEndPower")
    public int taskEndPower;
    @SerializedName("navAbnormal")
    public String navAbnormal;
    @SerializedName("robotAbnormal")
    public String robotAbnormal;

    public TaskRecord() {

    }

    public TaskRecord(String title, int disinfectionMode, long taskStartTime, long taskEndTime, int taskStatus, int taskStartPower, int taskEndPower, String navAbnormal, String robotAbnormal) {
        this.title = title;
        this.disinfectionMode = disinfectionMode;
        this.taskStartTime = taskStartTime;
        this.taskEndTime = taskEndTime;
        this.taskStatus = taskStatus;
        this.taskStartPower = taskStartPower;
        this.taskEndPower = taskEndPower;
        this.navAbnormal = navAbnormal;
        this.robotAbnormal = robotAbnormal;
    }

    public static TaskRecord create(String title, int disinfectionMode, long taskStartTime, int taskStatus, int taskStartPower, String navAbnormal, String robotAbnormal) {
        return new TaskRecord(
                title,
                disinfectionMode == 0 ? 1 : 2,
                taskStartTime,
                new Date().getTime(),
                taskStatus,
                taskStartPower,
                BatteryUtils.getPowerLevel(mApp),
                navAbnormal,
                robotAbnormal
        );
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("TaskRecord{");
        sb.append("title='").append(title).append('\'');
        sb.append(", disinfectionMode=").append(disinfectionMode);
        sb.append(", taskStartTime=").append(taskStartTime);
        sb.append(", taskEndTime=").append(taskEndTime);
        sb.append(", taskStatus=").append(taskStatus);
        sb.append(", taskStartPower=").append(taskStartPower);
        sb.append(", taskEndPower=").append(taskEndPower);
        sb.append(", navAbnormal='").append(navAbnormal).append('\'');
        sb.append(", robotAbnormal='").append(robotAbnormal).append('\'');
        sb.append('}');
        return sb.toString();
    }
}

package com.reeman.robot.disinfection.presenter.impl;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.content.Intent;
import android.widget.Button;
import android.widget.TextView;

import com.elvishew.xlog.XLog;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.reeman.robot.disinfection.R;
import com.reeman.robot.disinfection.activities.ElevatorSettingActivity;
import com.reeman.robot.disinfection.activities.MapBuildingActivity;
import com.reeman.robot.disinfection.activities.ScheduledTaskListActivity;
import com.reeman.robot.disinfection.activities.SettingActivity;
import com.reeman.robot.disinfection.activities.TaskCreateActivity;
import com.reeman.robot.disinfection.activities.TaskExecutingActivity;
import com.reeman.robot.disinfection.activities.WiFiConnectActivity;
import com.reeman.robot.disinfection.base.BaseActivity;
import com.reeman.robot.disinfection.constants.Constant;
import com.reeman.robot.disinfection.contract.MainContract;
import com.reeman.robot.disinfection.event.Event;
import com.reeman.robot.disinfection.repository.entities.Task;
import com.reeman.robot.disinfection.request.factory.ServiceFactory;
import com.reeman.robot.disinfection.request.model.MapListResponse;
import com.reeman.robot.disinfection.request.model.PortMapping;
import com.reeman.robot.disinfection.request.service.RobotService;
import com.reeman.robot.disinfection.utils.BatteryUtils;
import com.reeman.robot.disinfection.utils.SpManager;
import com.reeman.robot.disinfection.utils.VoiceHelper;
import com.reeman.robot.disinfection.widgets.EasyDialog;

import java.lang.reflect.Method;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.List;
import java.util.Locale;
import java.util.StringTokenizer;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.annotations.NonNull;
import io.reactivex.rxjava3.core.Single;
import io.reactivex.rxjava3.core.SingleObserver;
import io.reactivex.rxjava3.core.SingleSource;
import io.reactivex.rxjava3.disposables.Disposable;
import io.reactivex.rxjava3.functions.Consumer;
import io.reactivex.rxjava3.functions.Function;
import io.reactivex.rxjava3.observers.DisposableMaybeObserver;
import io.reactivex.rxjava3.schedulers.Schedulers;

import static com.reeman.robot.disinfection.base.BaseApplication.controller;
import static com.reeman.robot.disinfection.base.BaseApplication.dbRepository;
import static com.reeman.robot.disinfection.base.BaseApplication.mApp;

public class MainPresenter implements MainContract.Presenter {
    private final MainContract.View view;
    private Task currentTask;
    private boolean navReloc = false;

    public MainPresenter(MainContract.View view) {
        this.view = view;
    }

    @Override
    public Task getCurrentTask() {
        return currentTask;
    }

    @Override
    public void getManualTask() {
        dbRepository.getManualTask()
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(new DisposableMaybeObserver<Task>() {
                    @Override
                    public void onSuccess(@NonNull Task task) {
                        currentTask = task;
                        view.onManualTaskLoaded(currentTask);
                    }

                    @Override
                    public void onError(@NonNull Throwable e) {
                    }

                    @Override
                    public void onComplete() {
                        currentTask = Task.defaultManualTask();
                        view.onManualTaskLoaded(currentTask);
                    }
                });
    }

    @Override
    public void getMostRecentTask() {
        dbRepository.getAllScheduledTask()
                .subscribeOn(Schedulers.io())
                .flatMap(new Function<List<Task>, SingleSource<Task>>() {
                    @Override
                    public SingleSource<Task> apply(List<Task> tasks) throws Throwable {
                        if (tasks.isEmpty()) {
                            return Single.just(null);
                        }
                        Task mostRecentTask = null;
                        long distance = Long.MAX_VALUE;
                        Calendar instance = Calendar.getInstance(Locale.getDefault());
                        Calendar startTime = Calendar.getInstance(Locale.getDefault());
                        for (Task task : tasks) {
                            //任务禁用或者今天不执行，跳过
                            if (!task.enabled || (task.repeatTime & (1 << (instance.get(Calendar.DAY_OF_WEEK) - 2))) == 0)
                                continue;
                            String[] split = task.startTime.split(":");
                            startTime.set(Calendar.HOUR_OF_DAY, Integer.parseInt(split[0]));
                            startTime.set(Calendar.MINUTE, Integer.parseInt(split[1]));
                            //开始时间在当前时间之前，跳过
                            if (startTime.before(instance)) continue;
                            long abs = startTime.getTime().getTime() - instance.getTime().getTime();
                            if (abs > 10_000 && abs < distance) {
                                distance = abs;
                                mostRecentTask = task;
                            }
                        }
                        return Single.just(mostRecentTask);
                    }
                })
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(new SingleObserver<Task>() {
                    @Override
                    public void onSubscribe(@NonNull Disposable d) {
                    }

                    @Override
                    public void onSuccess(@NonNull Task task) {
                        view.onMostRecentTaskLoaded(task);
                    }

                    @Override
                    public void onError(@NonNull Throwable e) {
                        view.onMostRecentTaskLoaded(null);
                    }
                });
    }

    @Override
    public void onCheckPermission(MainContract.Presenter presenter, Method method, Object[] args) {
        view.showLockScreenView(presenter, method, args);
    }

    @Override
    public void onGotoCharge(Context context) {
        //急停按下
        if (controller.getScramState() == 0) {
            VoiceHelper.play("voice_emergency_stop_turn_on");
            return;
        }
        if (BatteryUtils.isCharging(context)) {
            VoiceHelper.play("voice_already_in_charging");
            return;
        }
        VoiceHelper.play("voice_going_to_charging");
        view.onChargeTaskStart();
    }

    @Override
    public void onStartDisinfectionBtnClicked(Activity activity, boolean hasCheckDelay) {
        //急停按下
        if (controller.getScramState() == 0) {
            VoiceHelper.play("voice_turn_off_scram_stop_to_start_task");
            EasyDialog.getInstance(activity).warnError(activity.getString(R.string.voice_turn_off_scram_stop_to_start_task));
            return;
        }
        //适配器充电
        if (BatteryUtils.isAcCharging(activity)) {
            VoiceHelper.play("voice_cut_off_power_to_start_task");
            EasyDialog.getInstance(activity).warnError(activity.getString(R.string.voice_cut_off_power_to_start_task));
            return;
        }
        String portMappingStr = SpManager.getInstance().getString(Constant.PORT_MAPPING, null);
//        if (portMappingStr == null) {
//            EasyDialog.getInstance(context).warnError(context.getString(R.string.text_not_set_port_mapping));
//            return;
//        }
//        Type type = new TypeToken<ArrayList<PortMapping>>() {
//        }.getType();
//        List<PortMapping> portMappingList = new Gson().fromJson(portMappingStr, type);
//        if (portMappingList == null || portMappingList.size() == 0) {
//            EasyDialog.getInstance(context).warnError(context.getString(R.string.text_not_set_port_mapping));
//            return;
//        }
        if (getCurrentTask().disinfectionFloors == null || getCurrentTask().disinfectionFloors.length() < 1) {
            EasyDialog.getInstance(activity).warnError(activity.getString(R.string.text_choose_floors_first));
            return;
        }
        if (getCurrentTask().disinfectionFloors != null) {
            List<String> disinfectionFloors = new ArrayList<>();
            StringTokenizer stringTokenizer = new StringTokenizer(getCurrentTask().disinfectionFloors, "\n");
            while (stringTokenizer.hasMoreElements()) {
                disinfectionFloors.add(stringTokenizer.nextToken());
            }
            if (disinfectionFloors.size()<1){
                EasyDialog.getInstance(activity).warnError(activity.getString(R.string.text_choose_floors_first));
                return;
            }
        }
        if (!hasCheckDelay) {
            int delayTime = SpManager.getInstance().getInt(Constant.DELAY_TIME, Constant.DEFAULT_DELAY_TIME);
            if (delayTime != 0) {
                EasyDialog.getInstance(activity).warnWithScheduledUpdateDetail(activity.getString(R.string.text_going_to_start_task_in_future, delayTime), R.string.text_start_right_now, R.string.text_cancel_task, new EasyDialog.OnViewClickListener() {
                    @Override
                    public void onViewClick(Dialog dialog, int id) {
                        dialog.dismiss();
                        if (id == R.id.btn_confirm) {
                            onStartDisinfectionBtnClicked(activity, true);
                        }
                    }
                }, new EasyDialog.OnTimeStampListener() {
                    @Override
                    public void onTimestamp(TextView title, TextView content, Button cancelBtn, Button neutralBtn, Button confirmBtn, int current) {
                        content.setText(activity.getString(R.string.text_going_to_start_task_in_future, delayTime - current));
                    }

                    @Override
                    public void onTimeOut(EasyDialog dialog) {
                        dialog.dismiss();
                        onStartDisinfectionBtnClicked(activity, true);
                    }
                }, 1000, delayTime * 1000);
                return;
            }
        }

        VoiceHelper.play("voice_going_to_start_disinfection_task");
        Intent intent = new Intent(activity, TaskExecutingActivity.class);
        intent.putExtra("extra", getCurrentTask());
        activity.startActivityForResult(intent,1002);
    }

    @Override
    public void onChooseMap(Context context, String ipAddress) {
        EasyDialog.getLoadingInstance(context).loading(context.getString(R.string.text_loading_map_list));
        RobotService robotService = ServiceFactory.getRobotService();
        robotService.getMapList("http://" + ipAddress + "/reeman/history_map")
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(new Consumer<MapListResponse>() {
                    @Override
                    public void accept(MapListResponse mapListResponse) throws Throwable {
                        if (mapListResponse == null || mapListResponse.maps == null || mapListResponse.maps.isEmpty()) {
                            view.onMapListLoaded(Collections.emptyList(), false);
                            navReloc = false;
                            return;
                        }
                        view.onMapListLoaded(mapListResponse.maps, navReloc);
                        navReloc = false;
                    }
                }, new Consumer<Throwable>() {
                    @Override
                    public void accept(Throwable throwable) throws Throwable {
                        view.onMapListLoadedFailed(throwable);
                        navReloc = false;
                    }
                });
    }

    @Override
    public void onNowMapNotDefaultMap(Context context, String nowMap) {
        String defaultMap = SpManager.getInstance().getString(Constant.DEFAULT_MAP, "");
        if (!defaultMap.equals("") && !defaultMap.equals(nowMap)) {
            EasyDialog.getInstance(context).confirm(mApp.getString(R.string.text_confirm), mApp.getString(R.string.text_cancel), mApp.getString(R.string.text_detect_not_use_default_map), new EasyDialog.OnViewClickListener() {
                @Override
                public void onViewClick(Dialog dialog, int id) {
                    dialog.dismiss();
                    if (id == R.id.btn_confirm) {
                        pushToElevatorOut(context);
                    }
                }
            });
        }
    }

    private void pushToElevatorOut(Context context) {
        EasyDialog.getInstance(context).confirm(mApp.getString(R.string.text_confirm), mApp.getString(R.string.text_cancel), mApp.getString(R.string.text_push_robot_to_elevator_out), new EasyDialog.OnViewClickListener() {
            @Override
            public void onViewClick(Dialog dialog, int id) {
                dialog.dismiss();
                if (id == R.id.btn_confirm) {
                    navReloc = true;
                    onChooseMap(context, Event.getIpEvent().ipAddress);
                }
            }
        });
    }


    @Override
    public void onGotoElevatorSettingPage(Context context) {
        BaseActivity.start(context, ElevatorSettingActivity.class, null);
    }


    @Override
    public void onGotoSettingPage(Context context) {
        BaseActivity.start(context, SettingActivity.class);
    }

    @Override
    public void onGotoTaskCreatePage(Context context, Task currentTask) {
        BaseActivity.start(context, TaskCreateActivity.class, currentTask);
    }

    @Override
    public void onGotoScheduledTaskListPage(Context context) {
        BaseActivity.start(context, ScheduledTaskListActivity.class, null);
    }

    @Override
    public void onGotoWiFiPage(Context context) {
        BaseActivity.start(context, WiFiConnectActivity.class, null);
    }

    @Override
    public void onGotoMapBuildingPage(Context context) {
        BaseActivity.start(context, MapBuildingActivity.class, null);
    }
}

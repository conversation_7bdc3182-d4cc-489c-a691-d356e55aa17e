package com.reeman.robot.disinfection.request.factory;

import com.reeman.robot.disinfection.request.RetrofitClient;
import com.reeman.robot.disinfection.request.service.RobotService;

public class ServiceFactory {

    private static RobotService robotService;

    public static RobotService getRobotService() {
        if (robotService == null) {
            robotService = RetrofitClient.getInstance().create(RobotService.class);
        }
        return robotService;
    }
}

package com.reeman.robot.disinfection.widgets;

import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.os.Handler;
import android.os.Looper;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.EditText;
import android.widget.GridLayout;
import android.widget.TextView;

import com.reeman.robot.disinfection.R;
import com.reeman.robot.disinfection.constants.Constant;
import com.reeman.robot.disinfection.utils.SpManager;


public class ScreenUnlockDialog extends BaseDialog implements View.OnClickListener, DialogInterface.OnDismissListener {

    private final EditText etUnlockPassword;
    private Runnable successRunnable;
    private Runnable timeoutRunnable;
    private Handler mHandler;

    public ScreenUnlockDialog(Context context) {
        super(context);
        setCancelable(false);
        setOnDismissListener(this);
        setCanceledOnTouchOutside(false);
        WindowManager.LayoutParams attributes = getWindow().getAttributes();
        attributes.y = -50;
        getWindow().setAttributes(attributes);
        ViewGroup root = (ViewGroup) LayoutInflater.from(context).inflate(R.layout.layout_screen_unlock_dialog, null);
        Button btnConfirm = root.findViewById(R.id.btn_confirm);
        btnConfirm.setOnClickListener(this);
        etUnlockPassword = root.findViewById(R.id.et_unlock_password);
        GridLayout glLockScreenKeys = root.findViewById(R.id.gl_lock_screen_keys);
        for (int i = 0; i < glLockScreenKeys.getChildCount(); i++) {
            glLockScreenKeys.getChildAt(i).setOnClickListener(this);
        }
        setContentView(root);
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();

        if (mHandler != null) {
            mHandler.removeCallbacks(this.timeoutRunnable);
            mHandler.postDelayed(this.timeoutRunnable, 15_000);
        }

        if (id == R.id.btn_confirm) {
            if (onScreenUnlockEventListener == null) return;
            String s = etUnlockPassword.getText().toString();
            if (s.length() == 3 && "110".equals(s)) {
                if (mHandler != null) mHandler.removeCallbacks(timeoutRunnable);
                onScreenUnlockEventListener.onPasswordCorrect(this, successRunnable);
            } else if (s.length() == 4 && s.equals(SpManager.getInstance().getString(Constant.LOCK_SCREEN_PASSWORD, ""))) {
                if (mHandler != null) mHandler.removeCallbacks(timeoutRunnable);
                onScreenUnlockEventListener.onPasswordCorrect(this, successRunnable);
            } else {
                etUnlockPassword.setText("");
                onScreenUnlockEventListener.onPasswordError(this);
            }
        } else if (id == R.id.tv_delete) {
            String s = etUnlockPassword.getText().toString();
            if (s.length() > 0) {
                etUnlockPassword.setText(s.substring(0, s.length() - 1));
            }
        } else if (id == R.id.tv_clear) {
            etUnlockPassword.setText("");
        } else {
            String s = etUnlockPassword.getText().toString();
            if (s.length() < 4) {
                etUnlockPassword.setText(s + ((TextView) v).getText());
            }
        }
    }


    public void show(Runnable successRunnable) {
        this.successRunnable = successRunnable;
        super.show();
    }

    public void showTimeOut(Runnable runnable, Runnable successRunnable) {
        mHandler = new Handler(Looper.getMainLooper());
        this.timeoutRunnable = new Runnable() {
            @Override
            public void run() {
                dismiss();
                runnable.run();
                mHandler = null;
            }
        };
        this.successRunnable = successRunnable;
        mHandler.postDelayed(timeoutRunnable, 15_000);
        super.show();
    }

    private OnScreenUnlockEventListener onScreenUnlockEventListener;

    public void setOnScreenUnlockEventListener(OnScreenUnlockEventListener onScreenUnlockEventListener) {
        this.onScreenUnlockEventListener = onScreenUnlockEventListener;
    }

    @Override
    public void onDismiss(DialogInterface dialog) {
        if (mHandler != null) {
            mHandler.removeCallbacks(timeoutRunnable);
            mHandler.removeCallbacks(successRunnable);
        }
    }

    public interface OnScreenUnlockEventListener {
        void onPasswordCorrect(Dialog dialog, Runnable successRunnable);

        void onPasswordError(Dialog dialog);
    }
}

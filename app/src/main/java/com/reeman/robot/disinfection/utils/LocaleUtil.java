package com.reeman.robot.disinfection.utils;

import android.content.res.Configuration;
import android.content.res.Resources;
import android.util.DisplayMetrics;

import java.util.Locale;

public class LocaleUtil {

    public static void changeAppLanguage(Resources resources, int currentLanguageType) {
        Configuration configuration = resources.getConfiguration();
        DisplayMetrics displayMetrics = resources.getDisplayMetrics();
        Locale locale;
        switch (currentLanguageType) {
            case 1:
                locale = Locale.CHINA;
                break;
            case 2:
                locale = Locale.JAPAN;
                break;
            case 3:
                locale = Locale.KOREA;
                break;
            default:
                locale = Locale.ENGLISH;
                break;
        }
        configuration.locale = locale;
        resources.updateConfiguration(configuration, displayMetrics);
    }


    public static int getLocaleType() {
        String language = Locale.getDefault().getLanguage();
        switch (language) {
            case "zh":
                return 1;
            case "ja":
                return 2;
            case "ko":
                return 3;
            default:
                return 0;
        }
    }

    public static String getLocaleType(int localeType) {
        switch (localeType) {
            case 1:
                return "中文简体";
            case 2:
                return "日本語";
            case 3:
                return "한국인";
            default:
                return "English";
        }
    }

    public static String getLanguage(int localeType) {
        switch (localeType){
            case 1:
                return "cn-ZH";
            case 2:
                return "ja-JP";
            case 3:
                return "ko-KR";
            default:
                return "en-US";
        }
    }

    public static String getVoice(int localeType) {
        switch (localeType){
            case 1:
                return "zh-CN-XiaoxiaoNeural";
            case 2:
                return "ja-JP-NanamiNeural";
            case 3:
                return "ko-KR-SunHiNeural";
            default:
                return "en-US-JennyNeural";
        }
    }
}



package com.reeman.robot.disinfection.request.model;

public class ElevatorModel {
    private int code;
    private String msg;
    private int runningDirection;
    private short nowFloor;
    private int backdoorStatus;
    private int frontDoorStatus;

    @Override
    public String toString() {
        return "code=" + code +
                ", runningDirection=" + runningDirection +
                ", nowFloor=" + nowFloor +
                ", backdoorStatus=" + backdoorStatus +
                ", frontDoorStatus=" + frontDoorStatus;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public int getRunningDirection() {
        return runningDirection;
    }

    public void setRunningDirection(int runningDirection) {
        this.runningDirection = runningDirection;
    }

    public short getNowFloor() {
        return nowFloor;
    }

    public void setNowFloor(short nowFloor) {
        this.nowFloor = nowFloor;
    }

    public int getBackdoorStatus() {
        return backdoorStatus;
    }

    public void setBackdoorStatus(int backdoorStatus) {
        this.backdoorStatus = backdoorStatus;
    }

    public int getFrontDoorStatus() {
        return frontDoorStatus;
    }

    public void setFrontDoorStatus(int frontDoorStatus) {
        this.frontDoorStatus = frontDoorStatus;
    }

    public ElevatorModel(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }


    public ElevatorModel(int code, int runningDirection, short nowFloor, int backdoorStatus, int frontDoorStatus) {
        this.code = code;
        this.runningDirection = runningDirection;
        this.nowFloor = nowFloor;
        this.backdoorStatus = backdoorStatus;
        this.frontDoorStatus = frontDoorStatus;
    }


    public ElevatorModel() {
    }
}

package com.reeman.robot.disinfection.repository;

import com.reeman.robot.disinfection.repository.db.AppDataBase;
import com.reeman.robot.disinfection.repository.entities.Task;

import java.util.Calendar;
import java.util.List;
import java.util.Locale;

import io.reactivex.rxjava3.core.Maybe;
import io.reactivex.rxjava3.core.Single;

public class DbRepository {
    private static DbRepository sInstance;
    private final AppDataBase database;

    public DbRepository(AppDataBase database) {
        this.database = database;
    }

    public static DbRepository getInstance(final AppDataBase database) {
        if (sInstance == null) {
            synchronized (DbRepository.class) {
                if (sInstance == null) {
                    sInstance = new DbRepository(database);
                }
            }
        }
        return sInstance;
    }

    /**
     * 新增任务
     *
     * @param task
     * @return
     */
    public Single<Long> createTask(Task task) {
        return database.taskDao().newTask(task);
    }

    /**
     * 删除本地任务（假删，只是更新删除字段)
     *
     * @param task
     * @return
     */
    public Single<Integer> deleteTask(Task task) {
        return database.taskDao().deleteTask(task.tid);
    }

    /**
     * 删除本地任务（真删除)
     *
     * @param task
     * @return
     */
    public Single<Integer> deleteTaskDirectly(Task task) {
        return database.taskDao().deleteTaskDirectly(task.tid);
    }

    /**
     * 删除本地任务（真删除，同步)
     *
     * @param id
     * @return
     */
    public void deleteTaskDirectlySync(long id) {
        database.taskDao().deleteTaskDirectlySync(id);
    }

    /**
     * 更新手动任务
     *
     * @param task
     * @return
     */
    public Single<Integer> updateTask(Task task) {
        return database.taskDao().updateTask(task);
    }

    /**
     * 查询手动任务
     *
     * @return
     */
    public Maybe<Task> getManualTask() {
        return database.taskDao().getManualTask();
    }

    /**
     * 查询所有计划任务
     *
     * @return
     */
    public Single<List<Task>> getAllScheduledTask() {
        return database.taskDao().getAllScheduledTask();
    }

    /**
     * 查询所有计划任务
     *
     * @return
     */
    public List<Task> getAllScheduledTaskSync() {
        return database.taskDao().getAllScheduledTaskSync();
    }

    /**
     * 启用禁用任务
     *
     * @return
     */
    public Single<Integer> updateTaskEnabledState(long tid, boolean enable) {
        return database.taskDao().updateTaskEnableState(tid, enable);
    }

    /**
     * 获取最近的定时任务
     */
    public Maybe<Task> getMostRecentTask() {
        return database.taskDao().getMostRecentTask();
    }

    /**
     * 获取定时任务
     */
    public Maybe<List<Task>> getCurrentScheduleTask() {
        Calendar instance = Calendar.getInstance(Locale.getDefault());
        int dayOfWeek = instance.get(Calendar.DAY_OF_WEEK) - 2;
        if (dayOfWeek < 0) dayOfWeek += 7;
        return database.taskDao().getCurrentScheduleTask(1 << dayOfWeek);
    }

    /**
     * 获取所有没有同步的定时任务
     *
     * @return
     */
    public List<Task> getAllUnSyncTask() {
        return database.taskDao().getAllUnSyncTask();
    }

    /**
     * 更新本地任务的云端id和同步状态 根据主键tid
     *
     * @param tid
     */
    public void updateLocalTaskOnlineStateByPrimaryKey(long tid, long cloudId) {
        database.taskDao().updateLocalTaskOnlineStateByPrimaryKey(tid, cloudId);
    }

    /**
     * 更新本地任务的云端id和同步状态 根据rowId
     *
     * @param row
     */
    public void updateLocalTaskOnlineStateByRowId(long row, long cloudId) {
        database.taskDao().updateLocalTaskOnlineStateByRowId(row, cloudId);
    }

    /**
     * 更新同步状态字段根据主键
     *
     * @param tid
     */
    public void updateSyncStateByPrimaryKey(long tid) {
        database.taskDao().updateSyncStateByPrimaryKey(tid);
    }

    /**
     * 获取所有删除的未同步到云端的定时任务
     *
     * @return
     */
    public List<Task> getAllDeleteAndUnSyncTasks() {
        return database.taskDao().getAllDeleteAndUnSyncTasks();
    }

    /**
     * 获取所有本地创建的云端没有的定时任务
     *
     * @return
     */
    public List<Task> getAllUnCreatedTasks() {
        return database.taskDao().getAllUnCreatedTasks();
    }

    /**
     * 获取所有修改的未同步到云端的定时任务
     *
     * @return
     */
    public List<Task> getAllModifiedTask() {
        return database.taskDao().getAllModifiedTask();
    }

    /**
     * 删除所有本地定时任务
     */
    public void deleteAllScheduledTasks() {
        database.taskDao().deleteAllScheduledTasks();
    }

    /**
     * 云端拉取的任务全部存到本地
     *
     * @param list
     * @return
     */
    public List<Long> insertAllScheduleTasks(List<Task> list) {
        return database.taskDao().insertAllScheduleTasks(list);
    }
}

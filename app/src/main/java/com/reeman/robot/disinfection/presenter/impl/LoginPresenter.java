package com.reeman.robot.disinfection.presenter.impl;

import android.app.Activity;
import android.content.Context;
import android.content.SharedPreferences;
import android.text.TextUtils;

import com.reeman.robot.disinfection.R;
import com.reeman.robot.disinfection.activities.GuideActivity;
import com.reeman.robot.disinfection.constants.Constant;
import com.reeman.robot.disinfection.contract.LoginContract;
import com.reeman.robot.disinfection.request.factory.ServiceFactory;
import com.reeman.robot.disinfection.request.service.RobotService;
import com.reeman.robot.disinfection.utils.SpManager;
import com.reeman.robot.disinfection.widgets.EasyDialog;

import java.util.HashMap;
import java.util.Map;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.schedulers.Schedulers;



public class LoginPresenter implements LoginContract.Presenter {

    private final LoginContract.View view;

    public LoginPresenter(LoginContract.View view) {
        this.view = view;
    }

    @Override
    public void login(Context context, String username, String password) {
        if (TextUtils.isEmpty(username)) {
            view.onLoginEvent(context.getString(R.string.text_username_can_not_be_empty), false);
            return;
        }

        if (TextUtils.isEmpty(password)) {
            view.onLoginEvent(context.getString(R.string.text_wifi_password_can_not_be_empty), false);
            return;
        }
        EasyDialog.getLoadingInstance(context).loading(context.getString(R.string.text_is_login));
        RobotService robotService = ServiceFactory.getRobotService();
        Map<String, String> map = new HashMap<>();
        map.put("account", username);
        map.put("password", password);
        robotService
                .login(map)
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(loginResponse -> {
                    if (loginResponse == null || loginResponse.data == null || loginResponse.data.result == null) {
                        view.onLoginEvent(context.getString(R.string.text_login_failed), false);
                        return;
                    }
                    String accessToken = loginResponse.data.result.accessToken;
                    String refreshToken = loginResponse.data.result.refreshToken;
                    SharedPreferences.Editor edit = SpManager.getInstance().edit();
                    edit.putString(Constant.USERNAME, username);
                    edit.putString(Constant.PASSWORD, password);
                    edit.putString(Constant.ACCESS_TOKEN, accessToken);
                    edit.putString(Constant.REFRESH_TOKEN, refreshToken);
                    edit.apply();
                    view.onLoginEvent(context.getString(R.string.text_login_success), true);
                }, throwable -> view.onLoginEvent(context.getString(R.string.text_login_success) + ":" + throwable.getMessage(), false));
    }

    @Override
    public void onSkip(Activity context, String from, int finishResult) {
        if (GuideActivity.class.getSimpleName().equals(from)) {
            context.setResult(finishResult);
            context.finish();
            return;
        }
        context.finish();
    }
}

package com.reeman.robot.disinfection.contract;

import android.content.Context;

import com.reeman.robot.disinfection.activities.MapBuildingActivity;
import com.reeman.robot.disinfection.presenter.IPresenter;
import com.reeman.robot.disinfection.view.IView;

public interface MapBuildingContract {
    interface Presenter extends IPresenter {
        void changeToConstructMap();

        void changeToNavMode();

        void saveMap();

        int getPointType();

        void addPointCount();

        void resetPointCount();

        int getCurrentNavMode();

        void onRebuildMapClicked(Context context);

        void onAbandonMapBuildingClicked(Context context);

        void onSaveMapClicked(Context context);

        void onMarkPointClicked(Context context, int id);

        void onMarkPoint(Context context, String[] position);

        int getPointCount();

        void onExitDeployClicked(Context context);

        void onDisinfectionRouteTestClicked(Context context);

        void onNavRes(Context context, String rawData);

        void onPointNotFound(Context context);

        void onChargingPileNotFound(Context context);

        void cancelTest();

        void onPointFound(Context context);

        void onEmergencyStopStateChange(Context context, int emergencyStopState);

        void onPowerConnected(Context context);

        void onDockFailed(Context context);
    }

    interface View extends IView {

        void onEnteringMapBuildingMode(String prompt);

        void onEnteringNavMode(String prompt);

        void onConfirmExitDeploy();

        void onRouteTestProgressUpdate(String prompt);
    }
}

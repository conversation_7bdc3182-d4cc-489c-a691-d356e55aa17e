package com.reeman.robot.disinfection.activities;

import android.app.Dialog;
import android.content.Intent;
import android.os.BatteryManager;
import android.text.TextUtils;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;

import com.elvishew.xlog.XLog;
import com.reeman.robot.disinfection.R;
import com.reeman.robot.disinfection.base.BaseActivity;
import com.reeman.robot.disinfection.constants.Constant;
import com.reeman.robot.disinfection.contract.TaskExecutingContract;
import com.reeman.robot.disinfection.elevatorcontrol.ElevatorState;
import com.reeman.robot.disinfection.event.Event;
import com.reeman.robot.disinfection.presenter.impl.TaskExecutingPresenter;
import com.reeman.robot.disinfection.repository.entities.Task;
import com.reeman.robot.disinfection.utils.BatteryUtils;
import com.reeman.robot.disinfection.utils.TimeUtils;
import com.reeman.robot.disinfection.utils.ToastUtils;
import com.reeman.robot.disinfection.utils.VoiceHelper;
import com.reeman.robot.disinfection.widgets.EasyDialog;
import com.reeman.robot.disinfection.widgets.ScreenUnlockDialog;
import com.reeman.robot.disinfection.widgets.TakeTheElevatorDialog;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.Date;

import static com.reeman.robot.disinfection.base.BaseApplication.controller;
import static com.reeman.robot.disinfection.presenter.impl.TaskExecutingPresenter.TASK_STATE_PAUSE;
import static com.reeman.robot.disinfection.presenter.impl.TaskExecutingPresenter.TASK_STATE_RUNNING;

public class TaskExecutingActivity extends BaseActivity implements View.OnClickListener, TaskExecutingContract.View, ScreenUnlockDialog.OnScreenUnlockEventListener {

    private TextView tvPower;
    private TextView tvHostname;

    private Button btnPauseTask;

    private TextView tvTaskType;
    private TextView tvTaskMode;
    private TextView tvTaskTimeWaste;
    private TextView tvSwitchState;
    private TextView tvTaskStartTime;
    private TextView tvTaskTimeRemain;
    private TextView tvCurrentDestination;
    private TextView tvElevatorState;

    private TaskExecutingContract.Presenter presenter;
    private Task task;
    private Date startTime;
    private int powerLevel;
    private ScreenUnlockDialog screenLockWindow;
    private TakeTheElevatorDialog takeTheElevatorDialog;

    @Override
    protected boolean shouldResponse2TimeEvent() {
        return true;
    }

    @Override
    protected int getLayoutRes() {
        return R.layout.activity_task_executing;
    }

    @Override
    protected void initView() {
        tvHostname = $(R.id.tv_hostname);
        tvPower = $(R.id.tv_power);
        tvTaskType = $(R.id.tv_current_task_type);
        tvTaskMode = $(R.id.tv_current_task_mode);
        tvTaskStartTime = $(R.id.tv_current_task_start_time);
        tvTaskTimeWaste = $(R.id.tv_current_task_time_waste);
        tvTaskTimeRemain = $(R.id.tv_current_task_time_remain);
        tvSwitchState = $(R.id.tv_current_disinfection_switch_state);
        tvCurrentDestination = $(R.id.tv_current_destination);
        tvElevatorState = $(R.id.tv_elevator_state);

        btnPauseTask = $(R.id.btn_pause_and_resume_disinfection);
        Button btnFinishTask = $(R.id.btn_finish_task);
        btnPauseTask.setOnClickListener(this);
        btnFinishTask.setOnClickListener(this);
    }


    @Override
    protected void initData() {
        presenter = new TaskExecutingPresenter(this);
        task = getIntent().getParcelableExtra("extra");
        tvTaskType.setText(getString(R.string.text_current_task_type, task.taskType == 0 ? getString(R.string.text_manual_task) : getString(R.string.text_scheduled_task)));
//        tvTaskMode.setText(getString(R.string.text_current_task_mode, task.taskMode == 0 ? getString(R.string.text_single_mode) : getString(R.string.text_duration_mode)));

        startTime = new Date();
        tvTaskStartTime.setText(getString(R.string.text_current_task_start_time, TimeUtils.format(startTime)));
        tvTaskTimeWaste.setText(getString(R.string.text_current_task_time_waste, "00:00:00"));
//        if (task.taskMode == 1) {
//            tvTaskTimeRemain.setVisibility(View.VISIBLE);
//            tvTaskTimeRemain.setText(getString(R.string.text_current_task_time_remain, TimeUtils.format(new Date(task.durationTime))));
//        } else {
        tvTaskTimeRemain.setVisibility(View.GONE);
//        }
        if (task.switchMode == 0) {
            tvSwitchState.setText(getString(R.string.text_current_disinfection_switch_state, getString(R.string.text_open)));
        } else {
            tvSwitchState.setText(getString(R.string.text_current_disinfection_switch_state, getString(R.string.text_close)));
        }

        Intent batteryStatus = BatteryUtils.getBatteryStatus(this);
        refreshPowerState(batteryStatus);
    }


    @Override
    protected void onResume() {
        super.onResume();
        controller.getHostName();
        controller.getCurrentMap();
        //开始任务
        presenter.startTask(this, task, startTime, powerLevel);
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();
        switch (id) {
            case R.id.btn_pause_and_resume_disinfection:
                presenter.onBtnPauseOrResumeClicked(this);
                break;
            case R.id.btn_finish_task:
                presenter.onBtnFinishTaskClicked(this);
                break;
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        presenter.onPause();
    }

    //地图加载完成
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onMapObtained(Event.OnMapEvent event) {
        XLog.e("nowMap :" + event.map);
        presenter.onMapCurrent(event.map);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onApplyMapEvent(Event.OnApplyMapEvent event) {
        presenter.onApplyMapSuccess(event.map);
        controller.relocateByPoint(ElevatorState.elevatorIn);
        mHandler.postDelayed(() -> {
            presenter.onMarkPoint(this);
        }, 1000);
    }

    //更新任务状态
    @Override
    public void updateTaskProgress(int taskMode, long taskDuration, long waste) {
        //消耗的时长
        tvTaskTimeWaste.setText(getString(R.string.text_current_task_time_waste, TimeUtils.formatTime2(waste)));
        //剩余时长
        if (taskMode == 1) {
            tvTaskTimeRemain.setText(getString(R.string.text_current_task_time_remain, TimeUtils.formatTime2(taskDuration - waste)));
        }
    }

    //消毒开关打开
    @Override
    public void showDisinfectionSwitchTurnOnView() {
        tvSwitchState.setText(getString(R.string.text_current_disinfection_switch_state, getString(R.string.text_open)));
    }

    //消毒开关关闭
    @Override
    public void showDisinfectionSwitchTurnOffView() {
        tvSwitchState.setText(getString(R.string.text_current_disinfection_switch_state, getString(R.string.text_close)));
    }

    @Override
    public void showTaskCanceled() {
        EasyDialog.getInstance(this).warn(getString(R.string.text_task_was_canceled), new EasyDialog.OnViewClickListener() {
            @Override
            public void onViewClick(Dialog dialog, int id) {
                dialog.dismiss();
                finish();
            }
        });
    }

    @Override
    public void showTaskFinishedView() {
        if (EasyDialog.isShow()) EasyDialog.getInstance().dismiss();
        finish();
    }

    @Override
    public void showReturningJourneyView(String prompt) {
        EasyDialog.getInstance(this).onlyCancel(prompt, new EasyDialog.OnViewClickListener() {
            @Override
            public void onViewClick(Dialog dialog, int id) {
                dialog.dismiss();
                mHandler.removeCallbacks(chargeRunnable);
                controller.cancelNavigation();
                controller.setNavigating(false);
                if (controller.isChargingDocking()) {
                    controller.cancelCharge();
                    controller.setChargingDocking(false);
                }
                finish();
            }
        });
    }

    @Override
    public void showTaskStateView(int currentTaskState) {
        if (currentTaskState == TASK_STATE_RUNNING) {
            btnPauseTask.setText(getString(R.string.text_pause_task));
        } else if (currentTaskState == TASK_STATE_PAUSE) {
            btnPauseTask.setText(getString(R.string.text_resume_task));
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onPoseLoaded(Event.OnPositionEvent event) {
        presenter.onPositionLoaded(event.position);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onPointFoundEvent(Event.OnPointFoundEvent event) {
//        tvCurrentDestination.setText(getString(R.string.text_current_destination, presenter.getNextDestination()));
        presenter.onPointFound();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onHostnameObtained(Event.OnHostnameEvent event) {
        tvHostname.setText(event.hostname);
    }

    @Override
    protected void onCustomTimeStamp(Event.OnTimeEvent event) {
        if (event.eventType == 0) {
            presenter.onLowPower(this);
        }
    }

    @Override
    protected void onCustomPointNotFoundEvent(Event.OnPointNotFoundEvent event) {
        presenter.onPointNotFound(this);
    }

    @Override
    protected void onCustomNavResEvent(Event.OnNavResEvent event) {
        presenter.onNavRes(this, event.rawData);
    }

    @Override
    protected void onCustomEmergencyStopStateChange(int emergencyStopState) {
        if (emergencyStopState == 0) {
            if (screenLockWindow != null && screenLockWindow.isShowing())
                screenLockWindow.dismiss();
        }
        presenter.onEmergencyStopStateChange(this, emergencyStopState);
    }

    @Override
    protected void onCustomBatteryChange(Intent powerIntent) {
        refreshPowerState(powerIntent);
    }

    @Override
    protected void onCustomPowerConnected() {
        presenter.onStartCharge();
    }

    @Override
    protected void onCustomChargingPileNotFound() {
        presenter.onChargingPileNotFound(this);
    }

    @Override
    protected void onCustomDockFailed() {
        if (EasyDialog.isShow()) EasyDialog.getInstance().dismiss();
        VoiceHelper.play("voice_charging_dock_failed");
        EasyDialog.getInstance(this).warn(getString(R.string.voice_charging_dock_failed), new EasyDialog.OnViewClickListener() {
            @Override
            public void onViewClick(Dialog dialog, int id) {
                dialog.dismiss();
                finish();
            }
        });
    }

    @Override
    public void showCanNotReachChargingPileView(String error) {
        EasyDialog.getInstance(this).warn(error, new EasyDialog.OnViewClickListener() {
            @Override
            public void onViewClick(Dialog dialog, int id) {
                dialog.dismiss();
                finish();
            }
        });
    }

    @Override
    public void showApplyMap() {
    }

    @Override
    public void showTakeTheElevatorNaviPoint(String point) {
        runOnUiThread(() -> {
            tvCurrentDestination.setText(getString(R.string.text_current_destination, point));
        });
    }

    @Override
    public void showTaskEndByElevatorError(String msg) {
        Intent intent =new Intent();
        intent.putExtra(Constant.TASK_RESULT_TEXT,msg);
        setResult(-1,intent);
        finish();
//        try {
//            if (EasyDialog.isShow()) EasyDialog.getInstance().dismiss();
//            runOnUiThread(() -> {
//                EasyDialog.getInstance(this).warn(msg, (dialog, id) -> {
//                    dialog.dismiss();
//                    finish();
//                });
//            });
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
    }

    @Override
    public void showOnPortOpenFailed(Exception e) {
        runOnUiThread(() -> {
            EasyDialog.getInstance(this).warn(e.getMessage() == null ? getString(R.string.elevator_port_open_failed) : e.getMessage(), (dialog, id) -> {
                dialog.dismiss();
                finish();
            });
        });
    }

    @Override
    public void onPermissionRequestFailed() {
        runOnUiThread(() -> EasyDialog.getInstance(this).warn(getString(R.string.text_request_elevator_permission_failed), (dialog, id) -> {
            dialog.dismiss();
            finish();
        }));
    }

    @Override
    public void onShowElevatorDialog(String msg) {
        runOnUiThread(() -> {
            if (EasyDialog.isShow()) {
                EasyDialog.getInstance().update(R.id.tv_content, msg);
                return;
            }
            EasyDialog.getElevatorInstance(this).takeTheElevatorDialog(msg, (dialog, id) -> {
                dialog.dismiss();
                finish();
            });
        });
    }

    @Override
    public void onElevatorDialogDismiss() {
        runOnUiThread(() -> {
            if (EasyDialog.isShow())
                EasyDialog.getInstance().dismiss();
        });
    }


    @Override
    public void showFailedView(int code, String voice, String text) {
        if (!TextUtils.isEmpty(text)) {
            mHandler.postDelayed(() -> EasyDialog.getInstance(this).warn(text, (dialog, id) -> finish()), 500);
        }
    }

    @Override
    public void showOpenDoorSuccessView() {
        runOnUiThread(() -> ToastUtils.showShortToast(getString(R.string.text_open_door_success)));
    }

    @Override
    public void showCloseDoorSuccessView() {
        runOnUiThread(() -> ToastUtils.showShortToast(getString(R.string.text_close_door_success)));
    }

    @Override
    public void showChargingPileNotFound() {
        EasyDialog.getInstance(this).warn(getString(R.string.voice_not_found_charging_pile), new EasyDialog.OnViewClickListener() {
            @Override
            public void onViewClick(Dialog dialog, int id) {
                dialog.dismiss();
                finish();
            }
        });
    }

    //更新电量
    private void refreshPowerState(Intent powerIntent) {
        powerLevel = powerIntent.getIntExtra(BatteryManager.EXTRA_LEVEL, 0);
        tvPower.setText(powerLevel + "%");
        int resId = getResources().getIdentifier("electricity" + (int) (powerLevel / 20.0), "drawable", getPackageName());
        tvPower.setCompoundDrawablesWithIntrinsicBounds(resId, 0, 0, 0);
    }

    //传感器异常
    @Override
    public void showSensorErrorView(String hardwareError) {
        EasyDialog.getInstance(this).warn(hardwareError, new EasyDialog.OnViewClickListener() {
            @Override
            public void onViewClick(Dialog dialog, int id) {
                dialog.dismiss();
                finish();
            }
        });
    }

    //显示锁屏界面，密码正确，恢复任务
    @Override
    public void showLockScreenView(Runnable runnable) {
        screenLockWindow = new ScreenUnlockDialog(this);
        screenLockWindow.setOnScreenUnlockEventListener(this);
        screenLockWindow.show(runnable);
    }

    //显示锁屏界面，超时继续任务，成功暂停任务
    @Override
    public void showLockScreenTimeOutView(Runnable timeoutRunnable, Runnable successRunnable) {
        screenLockWindow = new ScreenUnlockDialog(this);
        screenLockWindow.setOnScreenUnlockEventListener(this);
        screenLockWindow.showTimeOut(timeoutRunnable, successRunnable);
    }

    //密码正确
    @Override
    public void onPasswordCorrect(Dialog dialog, Runnable successRunnable) {
        dialog.dismiss();
        successRunnable.run();
    }

    //密码错误
    @Override
    public void onPasswordError(Dialog dialog) {
        ToastUtils.showShortToast(getString(R.string.text_password_error));
    }
}
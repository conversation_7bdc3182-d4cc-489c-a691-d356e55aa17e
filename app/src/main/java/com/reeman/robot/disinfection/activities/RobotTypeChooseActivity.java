package com.reeman.robot.disinfection.activities;

import android.content.SharedPreferences;
import android.view.View;
import android.widget.Button;
import android.widget.RadioGroup;

import com.reeman.robot.disinfection.R;
import com.reeman.robot.disinfection.base.BaseActivity;
import com.reeman.robot.disinfection.constants.Constant;
import com.reeman.robot.disinfection.utils.SpManager;


public class RobotTypeChooseActivity extends BaseActivity implements View.OnClickListener {

    private RadioGroup rgRobotTypeControl;
    private String from;
    private int result;

    @Override
    protected int getLayoutRes() {
        return R.layout.activity_robot_type_choose;
    }

    @Override
    protected void initView() {
        rgRobotTypeControl = $(R.id.rg_robot_type_control);
        Button btnConfirm = $(R.id.btn_confirm);
        btnConfirm.setOnClickListener(this);
    }

    @Override
    protected void initData() {
        from = getIntent().getStringExtra(Constant.EXTRA);
        result = getIntent().getIntExtra(Constant.EXTRA_INT, 0);
    }

    @Override
    public void onClick(View v) {
        onSave();
    }

    private void onSave() {
        SharedPreferences.Editor edit = SpManager.getInstance().edit();
        edit.putInt(Constant.ROBOT_TYPE, rgRobotTypeControl.getCheckedRadioButtonId() == R.id.rb_atomization_disinfection_robot ? 1 : 2);
        edit.apply();
        if (GuideActivity.class.getSimpleName().equals(from)) {
            setResult(result);
        }
        finish();
    }
}
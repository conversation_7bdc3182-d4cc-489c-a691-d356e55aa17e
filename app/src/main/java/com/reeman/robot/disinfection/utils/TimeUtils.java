package com.reeman.robot.disinfection.utils;

import android.annotation.SuppressLint;

import com.reeman.robot.disinfection.R;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;

import static com.reeman.robot.disinfection.base.BaseApplication.mApp;

/**
 * @ClassName: TimeUtils.java
 * @Author: XueDong(1123988589 @ qq.com)
 * @Date: 2022/1/9 15:03
 * @Description: 时间处理工具类
 */
public class TimeUtils {

    static SimpleDateFormat formatter = new SimpleDateFormat("HH:mm:ss", Locale.getDefault());

    /**
     * HH:mm:ss 格式转换为 秒数
     *
     * @param stayTime
     * @return
     */
    public static long str2Time(String stayTime) {
        int hour = Integer.parseInt(stayTime.substring(0, 2));
        int minute = Integer.parseInt(stayTime.substring(3, 5));
        int second = Integer.parseInt(stayTime.substring(6, 8));
        return hour * 3600 + minute * 60 + second;
    }

    /**
     * 毫秒数 转换为 HH时 mm分 ss秒格式
     *
     * @param time
     * @return
     */
    public static String formatTime(long time) {
        int hour = (int) (time / 3600);
        long minute = (time - hour * 3600) / 60;
        int second = (int) (time - hour * 3600 - minute * 60);
        return String.format(Locale.getDefault(), mApp.getString(R.string.text_time_duration_format), hour, minute, second);
    }

    /**
     * 毫秒数 转换为 HH时 mm分 ss秒格式
     *
     * @param time
     * @return
     */
    public static String formatTime2(long time) {
        int hour = (int) (time / 3600);
        long minute = (time - hour * 3600) / 60;
        int second = (int) (time - hour * 3600 - minute * 60);
        return String.format(Locale.getDefault(), mApp.getString(R.string.text_time_duration_format2), hour, minute, second);
    }


    /**
     * date 格式取出  HH时 mm分 ss秒
     *
     * @param date
     * @return
     */
    public static String formatTime(Date date) {
        SimpleDateFormat formatter = new SimpleDateFormat(mApp.getString(R.string.text_date_format), Locale.getDefault());
        return formatter.format(date);
    }


    public static String formatHourAndMinute(Date startTime) {
        SimpleDateFormat formatter = new SimpleDateFormat("HH:mm", Locale.getDefault());
        return formatter.format(startTime);
    }


    public static String format(Date date) {
        return formatter.format(date);
    }

}

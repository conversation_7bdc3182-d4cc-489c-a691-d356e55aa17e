package com.reeman.robot.disinfection.contract;

import android.content.Context;

import com.reeman.robot.disinfection.presenter.IPresenter;
import com.reeman.robot.disinfection.request.model.MapListResponse;
import com.reeman.robot.disinfection.view.IView;

import java.util.List;

public interface ElevatorSettingContract {
    interface Presenter extends IPresenter {
        void onInitEvent(Context context,boolean isLocalServer,String url);

        void onChooseMap(Context context, String ipAddress);

    }

    interface View extends IView {

        void onMapListLoaded(List<MapListResponse.Map> mapList);

        void onMapListLoadedFailed(Throwable throwable);



    }
}

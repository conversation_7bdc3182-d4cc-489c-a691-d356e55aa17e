package com.reeman.robot.disinfection.request.model;

import com.google.gson.annotations.SerializedName;
import com.reeman.robot.disinfection.repository.entities.Task;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.Date;
import java.util.List;

public class TaskListResponse {


    @SerializedName("code")
    public int code;
    @SerializedName("msg")
    public String msg;
    @SerializedName("data")
    public DataDTO data;

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("TaskListResponse{");
        sb.append("code=").append(code);
        sb.append(", msg='").append(msg).append('\'');
        sb.append(", data=").append(data);
        sb.append('}');
        return sb.toString();
    }

    public static class DataDTO {
        @SerializedName("result")
        public List<ResultDTO> result;
        @SerializedName("totalNumber")
        public int totalNumber;

        @Override
        public String toString() {
            final StringBuilder sb = new StringBuilder("DataDTO{");
            sb.append("result=").append(result);
            sb.append(", totalNumber=").append(totalNumber);
            sb.append('}');
            return sb.toString();
        }

        public static class ResultDTO {
            @SerializedName("id")
            public int id;
            @SerializedName("updateTime")
            public long updateTime;
            @SerializedName("disinfectionRobotId")
            public int disinfectionRobotId;
            @SerializedName("title")
            public String title;
            @SerializedName("disinfectionMode")
            public int disinfectionMode;
            @SerializedName("begin")
            public String begin;
            @SerializedName("repeat")
            public String repeat;
            @SerializedName("afterComplete")
            public int afterComplete;
            @SerializedName("disinfectionSwitch")
            public int disinfectionSwitch;
            @SerializedName("stayTime")
            public int stayTime;
            @SerializedName("navigationMode")
            public int navigationMode;
            @SerializedName("cycleTime")
            public int cycleTime;
            @SerializedName("taskSwitch")
            public int taskSwitch;

            @Override
            public String toString() {
                final StringBuilder sb = new StringBuilder("ResultDTO{");
                sb.append("id=").append(id);
                sb.append(", updateTime=").append(updateTime);
                sb.append(", disinfectionRobotId=").append(disinfectionRobotId);
                sb.append(", title='").append(title).append('\'');
                sb.append(", disinfectionMode=").append(disinfectionMode);
                sb.append(", begin='").append(begin).append('\'');
                sb.append(", repeat='").append(repeat).append('\'');
                sb.append(", afterComplete=").append(afterComplete);
                sb.append(", disinfectionSwitch=").append(disinfectionSwitch);
                sb.append(", stayTime=").append(stayTime);
                sb.append(", navigationMode=").append(navigationMode);
                sb.append(", cycleTime=").append(cycleTime);
                sb.append(", taskSwitch=").append(taskSwitch);
                sb.append('}');
                return sb.toString();
            }
        }
    }

    public static Task convertTo(DataDTO.ResultDTO task) throws JSONException {
        JSONObject jsonObject = new JSONObject(task.repeat);
        int[] flags = {jsonObject.optInt("Monday"),
                jsonObject.optInt("Tuesday"),
                jsonObject.optInt("Wednesday"),
                jsonObject.optInt("Thursday"),
                jsonObject.optInt("Friday"),
                jsonObject.optInt("Saturday"),
                jsonObject.optInt("Sunday")};
        int repeatMode = 0;

        for (int i = 0; i < flags.length; i++) {
            repeatMode = repeatMode | (flags[i] << i);
        }

        return new Task(
                task.id,
                1,
                task.title,
                task.disinfectionMode == 1 ? 0 : 1,
                task.disinfectionSwitch == 2 ? 0 : task.disinfectionSwitch == 1 ? 1 : 2,
                task.afterComplete == 3 ? 0 : 1,
                task.stayTime,
                task.cycleTime,
                task.begin,
                repeatMode,
                task.taskSwitch == 1,
                new Date(task.updateTime),
                true,
                false
        );
    }
}

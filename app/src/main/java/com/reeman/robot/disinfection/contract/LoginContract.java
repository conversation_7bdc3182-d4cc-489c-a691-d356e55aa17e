package com.reeman.robot.disinfection.contract;

import android.app.Activity;
import android.content.Context;

import com.reeman.robot.disinfection.presenter.IPresenter;
import com.reeman.robot.disinfection.view.IView;

public interface LoginContract {

    interface Presenter extends IPresenter {

        void login(Context context, String username, String password);

        void onSkip(Activity context, String from, int finishResult);
    }

    interface View extends IView {

        void onLoginEvent(String string, boolean result);
    }
}

package com.reeman.robot.disinfection.presenter.impl;

import android.content.Context;

import com.reeman.robot.disinfection.R;
import com.reeman.robot.disinfection.constants.Constant;
import com.reeman.robot.disinfection.contract.ElevatorSettingContract;
import com.reeman.robot.disinfection.request.factory.ServiceFactory;
import com.reeman.robot.disinfection.request.model.MapListResponse;
import com.reeman.robot.disinfection.request.service.RobotService;
import com.reeman.robot.disinfection.utils.SpManager;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.functions.Consumer;
import io.reactivex.rxjava3.schedulers.Schedulers;


public class ElevatorSettingPresenter implements ElevatorSettingContract.Presenter {

    private final ElevatorSettingContract.View view;

    public ElevatorSettingPresenter(ElevatorSettingContract.View view) {
        this.view = view;
    }

    @Override
    public void onInitEvent(Context context,boolean isLocalServer,String url) {
    }

    @Override
    public void onChooseMap(Context context, String ipAddress) {
        RobotService robotService = ServiceFactory.getRobotService();
        robotService.getMapList("http://" + ipAddress + "/reeman/history_map")
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(mapListResponse -> {
                    if (mapListResponse == null || mapListResponse.maps == null || mapListResponse.maps.isEmpty()) {
                        view.onMapListLoadedFailed(new Throwable(context.getString(R.string.text_can_not_find_map)));
                        return;
                    }
                    StringBuilder stringBuilder = new StringBuilder();
                    for (MapListResponse.Map map : mapListResponse.maps) {
                        stringBuilder.append(map.name).append("\n");
                    }
                    stringBuilder.deleteCharAt(stringBuilder.length() - 1);
                    SpManager.getInstance().edit().putString(Constant.LOCAL_MAPS, stringBuilder.toString()).apply();
                    view.onMapListLoaded(mapListResponse.maps);
                }, view::onMapListLoadedFailed);
    }

}

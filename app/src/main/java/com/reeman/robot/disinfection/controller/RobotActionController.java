package com.reeman.robot.disinfection.controller;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.BatteryManager;
import android.util.Log;

import com.elvishew.xlog.XLog;
import com.reeman.nerves.RobotActionProvider;
import com.reeman.robot.disinfection.BuildConfig;
import com.reeman.robot.disinfection.activities.MainActivity;
import com.reeman.robot.disinfection.base.BaseActivity;
import com.reeman.robot.disinfection.constants.Constant;
import com.reeman.robot.disinfection.event.Event;
import com.reeman.robot.disinfection.repository.entities.Task;
import com.reeman.robot.disinfection.utils.SpManager;
import com.reeman.robot.disinfection.utils.TimeUtils;
import com.reeman.robot.disinfection.utils.WIFIUtils;
import com.rsc.impl.OnROSListener;
import com.rsc.reemanclient.ConnectServer;

import org.greenrobot.eventbus.EventBus;

import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import io.reactivex.rxjava3.annotations.NonNull;
import io.reactivex.rxjava3.core.MaybeObserver;
import io.reactivex.rxjava3.core.SingleObserver;
import io.reactivex.rxjava3.disposables.Disposable;
import io.reactivex.rxjava3.schedulers.Schedulers;

import static com.reeman.robot.disinfection.base.BaseApplication.controller;
import static com.reeman.robot.disinfection.base.BaseApplication.dbRepository;
import static com.reeman.robot.disinfection.base.BaseApplication.mApp;


public class RobotActionController {

    private static RobotActionController INSTANCE;
    private final RobotActionProvider robotActionProvider;
    private boolean isNavigating = false;
    private boolean isApplyingMap = false;
    private String applyMapResult;
    private boolean isChargingDocking = false;
    private String map;

    public static RobotActionController getInstance() {
        if (INSTANCE == null) {
            synchronized (RobotActionController.class) {
                if (INSTANCE == null) {
                    INSTANCE = new RobotActionController();
                }
            }
        }
        return INSTANCE;
    }

    private RobotActionController() {
        ConnectServer server = ConnectServer.getInstance(mApp);
        robotActionProvider = RobotActionProvider.getInstance();
        RosCallback callback = new RosCallback();
        server.registerROSListener(callback);
        RobotReceiver robotReceiver = new RobotReceiver();
        mApp.registerReceiver(robotReceiver, new RobotIntentFilter());
    }

    public int getScramState() {
        return robotActionProvider.getScramState();
    }

    public void setGlobalRadius(double r) {
        robotActionProvider.sendRosCom("through_radius[set_global_radius:" + r + "]");
    }

    public void startDisinfection() {
        XLog.w("打开消毒开关");
        robotActionProvider.sendRosCom("sys:lock");
    }

    public void stopDisinfection() {
        XLog.w("关闭消毒开关");
        robotActionProvider.sendRosCom("sys:unlock");
    }

    public void setNavSpeed(String navSpeed) {
        robotActionProvider.sendRosCom((Event.getVersionCodeEvent() >= 218 ? "write_" : "") + "max_vel[" + navSpeed + "]");
    }

    public void getNavSpeed() {
        robotActionProvider.sendRosCom("get_max_vel");
    }

    public void applyMap(String map) {
        isApplyingMap = true;
        robotActionProvider.sendRosCom("call_web[apply_map:" + map + "]");
    }

    public String getApplyMapResult() {
        return applyMapResult;
    }

    public void setApplyMapResult(String applyMapResult) {
        this.applyMapResult = applyMapResult;
    }


    public boolean isNavigating() {
        return isNavigating;
    }

    public void setNavigating(boolean navigating) {
        isNavigating = navigating;
    }

    public boolean isChargingDocking() {
        return isChargingDocking;
    }

    public void setChargingDocking(boolean chargingDocking) {
        isChargingDocking = chargingDocking;
    }

    public class RosCallback extends OnROSListener {
        @Override
        public void onResult(String result) {
            if (result.startsWith("laser") || result.startsWith("visual_mark"))
                return;
            Log.w(BuildConfig.TAG, result);
            if (result.startsWith("ver")) {
                EventBus.getDefault().post(Event.getVersionEvent(result));
            } else if (result.startsWith("nav_res")) {
                EventBus.getDefault().post(Event.getNavResEvent(result));
            } else if (result.startsWith("current_map")) {
                EventBus.getDefault().post(Event.getMapEvent(result));
            } else if (result.startsWith("sys:boot")) {
                EventBus.getDefault().post(Event.getHostnameEvent(result));
            } else if (result.startsWith("ip")) {
                EventBus.getDefault().post(Event.getIpEvent(result));
            } else if (result.startsWith("move_status:4")) {
                EventBus.getDefault().post(Event.getEncounterObstacleEvent());
            } else if (result.startsWith("move_status:5")) {
                EventBus.getDefault().post(Event.getEncounterObstacleEvent());
            } else if (result.startsWith("point_charge:1")) {
                EventBus.getDefault().post(Event.getChargePointNotFoundEvent());
            } else if (result.startsWith("point:0")) {
                EventBus.getDefault().post(Event.getOnPointFoundEvent());
            } else if (result.startsWith("point:1")) {
                EventBus.getDefault().post(Event.getPointNotFoundEvent());
            } else if (result.startsWith("model")) {
                EventBus.getDefault().post(Event.getOnNavModeEvent(Integer.parseInt(result.replace("model:", ""))));
            } else if (result.startsWith("initpose:0")) {
                XLog.w("initpose0  "+isApplyingMap+"   "+map);
                if (isApplyingMap && !map.equals("")) {
                    isApplyingMap = false;
                    EventBus.getDefault().post(Event.getOnApplyMapEvent(map));
                    return;
                }
                EventBus.getDefault().post(Event.getOnInitPoseEvent());
            } else if (result.startsWith("nav:pose[")) {
                EventBus.getDefault().post(Event.getOnPositionEvent(result.replace("nav:pose[", "").replace("]", "")));
            } else if (result.startsWith("set_flag_point:success")) {
                EventBus.getDefault().post(Event.getMarkSuccessEvent());
            } else if (result.startsWith("set_flag_point:failed")) {
                EventBus.getDefault().post(Event.getMarkFailedEvent());
            } else if (result.startsWith("get_max_vel")) {
                EventBus.getDefault().post(Event.getOnSpeedEvent(result.replace("get_max_vel:", "").trim()));
            } else if (result.startsWith("wifi:connect fail")) {
                EventBus.getDefault().post(Event.getOnWiFiEvent(false));
            } else if (result.startsWith("wifi:connect success")) {
                EventBus.getDefault().post(Event.getOnWiFiEvent(true));
            } else if (result.startsWith("apply_map[map_name:")) {
                map = result.replace("apply_map[map_name:", "").replace("]", "");
//                EventBus.getDefault().post(Event.getOnApplyMapEvent(result.replace("apply_map[map_name:", "").replace("]", "")));
            } else if (result.startsWith("special_plan")) {
                XLog.w("收到新特殊区：" + result);
                EventBus.getDefault().post(Event.getOnSpecialPlanEvent(result.replace("special_plan:", "")));
            }
        }
    }

    public class RobotReceiver extends BroadcastReceiver {
        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            Log.w("xuedong", action);
            switch (action) {
                case Intent.ACTION_TIME_TICK:
                    onTimeStamp();
                    break;
                case Intent.ACTION_POWER_CONNECTED:
                case Intent.ACTION_POWER_DISCONNECTED:
                case Intent.ACTION_BATTERY_CHANGED:
                    EventBus.getDefault().post(Event.getPowerEvent(intent));
                    break;
                case "AUTOCHARGE_ERROR_DOCKNOTFOUND":
                case "AUTOCHARGE_ERROR_DOCKINGFAILURE":
                    EventBus.getDefault().post(Event.getDockFailedEvent());
                    break;
                case "REEMAN_BROADCAST_SCRAMSTATE":
                    EventBus.getDefault().post(Event.getEmergencyStopEvent(intent));
                    break;
                case "android.net.conn.CONNECTIVITY_CHANGE":
                    EventBus.getDefault().post(Event.getNetworkEvent(intent));
                    break;
            }
        }


        private void onTimeStamp() {
            //更新最近任务
            List<Activity> activities = BaseActivity.activities;
            if (activities.isEmpty()) return;
            if (activities.get(activities.size() - 1) instanceof MainActivity) {
                dbRepository.getAllScheduledTask()
                        .subscribeOn(Schedulers.io())
                        .observeOn(Schedulers.io())
                        .subscribeWith(new SingleObserver<List<Task>>() {
                            @Override
                            public void onSubscribe(@NonNull Disposable d) {

                            }

                            @Override
                            public void onSuccess(@NonNull List<Task> tasks) {
                                if (activities.get(activities.size() - 1) instanceof MainActivity)
                                    updateRecentTask(tasks);
                            }

                            @Override
                            public void onError(@NonNull Throwable e) {
                                EventBus.getDefault().post(Event.getOnMostRecentTaskLoadEvent(null));
                            }
                        });
            }


            //急停打开不处理
            if (getScramState() == 0) return;

            //线充不处理
            IntentFilter intentFilter = new IntentFilter(Intent.ACTION_BATTERY_CHANGED);
            Intent batteryStatusIntent = mApp.registerReceiver(null, intentFilter);
            int chargePlug = batteryStatusIntent.getIntExtra(BatteryManager.EXTRA_PLUGGED, 0);
            if (chargePlug == BatteryManager.BATTERY_PLUGGED_AC) return;

            //低电
            int currentPowerLevel = batteryStatusIntent.getIntExtra(BatteryManager.EXTRA_LEVEL, 0);
            int lowPowerThreshold = SpManager.getInstance().getInt(Constant.LOW_POWER, Constant.DEFAULT_LOW_POWER);
            if (currentPowerLevel <= lowPowerThreshold) {
                //充电桩充电中不处理
                if (chargePlug == BatteryManager.BATTERY_PLUGGED_WIRELESS) return;
                //下发充电任务
                EventBus.getDefault().post(Event.getTimeEvent(0, null));
                return;
            }

            //检查有无定时任务
            dbRepository.getCurrentScheduleTask()
                    .subscribeOn(Schedulers.io())
                    .observeOn(Schedulers.io())
                    .subscribeWith(new MaybeObserver<List<Task>>() {
                        @Override
                        public void onSubscribe(@NonNull Disposable d) {

                        }

                        @Override
                        public void onSuccess(@NonNull List<Task> tasks) {
                            Calendar calendar = Calendar.getInstance();
                            Date current = new Date();
                            for (Task task : tasks) {
                                try {
                                    String[] split = task.startTime.split(":");
                                    calendar.set(Calendar.HOUR_OF_DAY, Integer.parseInt(split[0]));
                                    calendar.set(Calendar.MINUTE, Integer.parseInt(split[1]));
                                } catch (Exception e) {
                                    continue;
                                }
                                long abs = Math.abs(calendar.getTime().getTime() - current.getTime());
                                if (abs <= 10_000) {
                                    XLog.w("执行定时任务：" + task.taskName + " 相隔时间：" + abs);
                                    EventBus.getDefault().post(Event.getTimeEvent(1, task));
                                    return;
                                }
                            }
                            XLog.w("未发现可以执行的定时任务");
                        }

                        @Override
                        public void onError(@NonNull Throwable e) {

                        }

                        @Override
                        public void onComplete() {

                        }
                    });


        }
    }

    public void updateRecentTask(List<Task> tasks) {
        if (tasks.isEmpty()) {
            EventBus.getDefault().post(Event.getOnMostRecentTaskLoadEvent(null));
            return;
        }
        Task mostRecentTask = null;
        long distance = Long.MAX_VALUE;
        Calendar instance = Calendar.getInstance(Locale.getDefault());
        Calendar startTime = Calendar.getInstance(Locale.getDefault());
        for (Task task : tasks) {
            //任务禁用或者今天不执行，跳过
            if (!task.enabled || (task.repeatTime & (1 << (instance.get(Calendar.DAY_OF_WEEK) - 2))) == 0)
                continue;
            String[] split = task.startTime.split(":");
            startTime.set(Calendar.HOUR_OF_DAY, Integer.parseInt(split[0]));
            startTime.set(Calendar.MINUTE, Integer.parseInt(split[1]));
            //开始时间在当前时间之前，跳过
            if (startTime.before(instance)) continue;
            long abs = startTime.getTime().getTime() - instance.getTime().getTime();
            if (abs > 10_000 && abs < distance) {
                distance = abs;
                mostRecentTask = task;
            }
        }
        EventBus.getDefault().post(Event.getOnMostRecentTaskLoadEvent(mostRecentTask));
    }

    public void getHostName() {
        robotActionProvider.sendRosCom("hostname:get");
    }

    public void getHostIp() {
        robotActionProvider.sendRosCom("ip:request");
    }

    public void getCurrentMap() {
        robotActionProvider.sendRosCom("nav:current_map");
    }

    public void markPoint(String[] arr, String point) {
        XLog.w("标点: " + arr[0] + " " + arr[1] + " " + arr[2] + " " + point);
        robotActionProvider.sendRosCom("nav:set_flag_point[" + arr[0] + "," + arr[1] + "," + arr[2] + "," + point + "]");
    }

    public void saveMap() {
        XLog.w("保存地图: ");
        robotActionProvider.sendRosCom("save_map");
    }

    public void changeMode(int mode) {
        XLog.w("改变导航模式: " + mode);
        robotActionProvider.sendRosCom("model:" + mode);
    }

    public void getNavMode() {
        XLog.w("获取导航模式");
        robotActionProvider.sendRosCom("model:0");
    }

    public void navigationByPoint(String point) {
        XLog.w("导航到: " + point);
        isNavigating = true;
        robotActionProvider.sendRosCom("point[" + point + "]");
    }

    public void getPointPosition(String point) {
        XLog.w("获取" + point + "位置");
        robotActionProvider.sendRosCom("nav:get_flag_point[" + point + "]");
    }

    public void getCurrentPosition() {
        XLog.w("获取当前位置");
        robotActionProvider.sendRosCom("nav:get_pose");
    }

    public void chargeByPoint(String chargePoint) {
        XLog.w("去充电: " + chargePoint);
        isNavigating = true;
        robotActionProvider.sendRosCom("point_charge[" + chargePoint + "]");
    }

    public void relocateByPoint(String point) {
        XLog.w("重定位: " + point);
        robotActionProvider.sendRosCom("nav:reloc_point[" + point + "]");
    }

    public void moveForward() {
        XLog.w("前进");
        robotActionProvider.moveFront(5, 10);
    }

    public void cancelNavigation() {
        XLog.w("取消导航");
        isNavigating = false;
        robotActionProvider.sendRosCom("cancel_goal");
    }

    public void cancelCharge() {
        XLog.w("取消充电");
        robotActionProvider.sendRosCom("bat:uncharge");
    }

    public void connectROSWifi(String wifiName, String wifiPass) {
        robotActionProvider.sendRosCom("wifi[ssid " + wifiName + ";pwd " + wifiPass + "]");
    }
}

package com.reeman.robot.disinfection.widgets;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.gongwen.marqueen.MarqueeFactory;
import com.gongwen.marqueen.MarqueeView;
import com.reeman.robot.disinfection.R;

import java.util.ArrayList;

public class TakeTheElevatorDialog extends BaseDialog {

    private TextView tvContent;

    public void setTvContent(String content){
        tvContent.setText(content);
    }

    public TakeTheElevatorDialog(@NonNull Context context, int inAnim, int outAnim, TakeTheElevatorListener takeTheElevatorListener) {
        super(context);
        setCanceledOnTouchOutside(false);
        View root = LayoutInflater.from(context).inflate(R.layout.layout_take_the_elevator_dialog, null);
        MarqueeView mvElevator = root.findViewById(R.id.mv_elevator);
        Button btnClose = root.findViewById(R.id.btn_close);
        tvContent = root.findViewById(R.id.tv_content);
        btnClose.setOnClickListener(view -> {
            takeTheElevatorListener.onclickCancelTask();
        });
        ElevatorIconView elevatorIconView = new ElevatorIconView(context);
        ArrayList<Integer> list = new ArrayList<>();
        list.add(R.drawable.ic_take_the_elevator);
        list.add(R.drawable.ic_take_the_elevator);
        elevatorIconView.setData(list);
        mvElevator.setMarqueeFactory(elevatorIconView);
        mvElevator.setInAndOutAnim(inAnim, outAnim);
        mvElevator.startFlipping();
        setContentView(root);
        Window window = getWindow();
        WindowManager.LayoutParams params = window.getAttributes();
        params.width = 720;
        params.height = 360;
        window.setAttributes(params);
    }


    @Override
    public void show() {
        super.show();
    }

    class ElevatorIconView extends MarqueeFactory<RelativeLayout, Integer> {

        private LayoutInflater inflater;

        public ElevatorIconView(Context mContext) {
            super(mContext);
            inflater = LayoutInflater.from(mContext);
        }

        @Override
        protected RelativeLayout generateMarqueeItemView(Integer data) {
            RelativeLayout view = (RelativeLayout) inflater.inflate(R.layout.layout_marquee_item, null);
            ImageView ivMarqueeItem = (ImageView) view.findViewById(R.id.iv_marquee_item);
            ivMarqueeItem.setImageResource(data);
            return view;
        }
    }

    public interface TakeTheElevatorListener {
        void onclickCancelTask();
    }
}

package com.reeman.robot.disinfection.presenter.impl;

import com.reeman.robot.disinfection.constants.Constant;
import com.reeman.robot.disinfection.contract.ScheduledTaskListContract;
import com.reeman.robot.disinfection.event.Event;
import com.reeman.robot.disinfection.repository.entities.Task;
import com.reeman.robot.disinfection.request.factory.ServiceFactory;
import com.reeman.robot.disinfection.utils.SpManager;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.annotations.NonNull;
import io.reactivex.rxjava3.core.Single;
import io.reactivex.rxjava3.functions.Consumer;
import io.reactivex.rxjava3.functions.Function;
import io.reactivex.rxjava3.observers.DisposableSingleObserver;
import io.reactivex.rxjava3.schedulers.Schedulers;

import static com.reeman.robot.disinfection.base.BaseApplication.dbRepository;

public class ScheduledTaskListPresenter implements ScheduledTaskListContract.Presenter {

    private final ScheduledTaskListContract.View view;
    private List<Task> currentTasks;

    public List<Task> getCurrentTasks() {
        return currentTasks;
    }

    public ScheduledTaskListPresenter(ScheduledTaskListContract.View view) {
        this.view = view;
    }

    /**
     * 获取所有定时任务
     */
    @Override
    public void getAllScheduledTaskList() {
        dbRepository.getAllScheduledTask()
                .subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeWith(new DisposableSingleObserver<List<Task>>() {

                    @Override
                    public void onSuccess(@NonNull List<Task> tasks) {
                        currentTasks = tasks;
                        view.onScheduledTaskListLoaded(tasks);
                    }

                    @Override
                    public void onError(@NonNull Throwable e) {
                        view.onScheduledTaskListLoadFailed(e.toString());
                    }
                });
    }

    /**
     * 修改定时任务启用状态
     *
     * @param position
     * @param task
     * @param enable
     */
    @Override
    public void updateTaskEnableState(int position, Task task, boolean enable) {
        dbRepository.updateTaskEnabledState(task.tid, enable)
                .subscribeOn(Schedulers.io())
                .observeOn(Schedulers.io())
                .map(new Function<Integer, Long>() {
                    @Override
                    public Long apply(Integer integer) throws Throwable {
                        view.onTaskEnableStateUpdateSuccess(position, enable);

                        //没登录，不同步任务
                        if (Constant.DEFAULT_USERNAME.equals(SpManager.getInstance().getString(Constant.USERNAME, Constant.DEFAULT_USERNAME))) {
                            return -1L;
                        }

                        if (task.cloudId != -1) {
                            try {
                                Map<String, Integer> map = new HashMap<>();
                                map.put("taskSwitch", enable ? 1 : 0);
                                ServiceFactory
                                        .getRobotService()
                                        .updateOnlineTaskEnableState(Event.getOnHostnameEvent().hostname, task.cloudId, map).execute();
                                return task.tid;
                            } catch (Exception e) {
                                return -1L;
                            }
                        }
                        return -1L;
                    }
                })
                .subscribe(new Consumer<Long>() {
                    @Override
                    public void accept(Long tid) throws Throwable {
                        if (tid != -1) {
                            dbRepository.updateSyncStateByPrimaryKey(task.tid);
                        }
                    }
                });

    }

    @Override
    public void deleteTask(int index, Task task) {
        Single<Integer> singleDeleteTask;

        if (task.cloudId == -1) {
            //该任务还没同步到云端，直接删除
            singleDeleteTask = dbRepository.deleteTaskDirectly(task);
        } else {
            singleDeleteTask = dbRepository.deleteTask(task);
        }

        singleDeleteTask
                .subscribeOn(Schedulers.io())
                .observeOn(Schedulers.io())
                .map(new Function<Integer, Long>() {
                    @Override
                    public Long apply(Integer integer) throws Throwable {
                        view.onTaskDeleted(index);

                        //如果没登录，直接修改任务状态为已同步
                        if (Constant.DEFAULT_USERNAME.equals(SpManager.getInstance().getString(Constant.USERNAME, Constant.DEFAULT_USERNAME))) {
                            return -1L;
                        }

                        if (task.cloudId != -1) {
                            try {
                                ServiceFactory
                                        .getRobotService()
                                        .deleteOnlineTask(Event.getOnHostnameEvent().hostname, task.cloudId)
                                        .execute();
                                return task.tid;
                            } catch (Exception e) {
                                return -1L;
                            }
                        }
                        return -1L;
                    }
                })
                .subscribe(new Consumer<Long>() {
                    @Override
                    public void accept(Long tid) throws Throwable {
                        if (tid != -1) {
                            dbRepository.updateSyncStateByPrimaryKey(tid);
                        }
                    }
                });

    }
}

package com.reeman.robot.disinfection.service;

import android.app.Activity;
import android.app.Service;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.BatteryManager;
import android.os.IBinder;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.Nullable;

import com.reeman.robot.disinfection.activities.MainActivity;
import com.reeman.robot.disinfection.activities.ScheduledTaskListActivity;
import com.reeman.robot.disinfection.activities.TaskCreateActivity;
import com.reeman.robot.disinfection.activities.TaskExecutingActivity;
import com.reeman.robot.disinfection.base.BaseActivity;
import com.reeman.robot.disinfection.constants.Constant;
import com.reeman.robot.disinfection.event.Event;
import com.reeman.robot.disinfection.repository.entities.Task;
import com.reeman.robot.disinfection.request.factory.ServiceFactory;
import com.reeman.robot.disinfection.request.model.OnlineTask;
import com.reeman.robot.disinfection.request.model.State;
import com.reeman.robot.disinfection.request.model.TaskListResponse;
import com.reeman.robot.disinfection.request.service.RobotService;
import com.reeman.robot.disinfection.utils.PackageUtils;
import com.reeman.robot.disinfection.utils.SpManager;
import com.reeman.robot.disinfection.widgets.EasyDialog;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import static com.reeman.robot.disinfection.base.BaseApplication.controller;
import static com.reeman.robot.disinfection.base.BaseApplication.dbRepository;
import static com.reeman.robot.disinfection.base.BaseApplication.mApp;

public class BackendService extends Service {

    private final Runnable task = new Runnable() {
        @Override
        public void run() {

            RobotService robotService = ServiceFactory.getRobotService();

            IntentFilter intentFilter = new IntentFilter(Intent.ACTION_BATTERY_CHANGED);
            Intent batteryStatusIntent = mApp.registerReceiver(null, intentFilter);

            int status;
            int chargePlug = batteryStatusIntent.getIntExtra(BatteryManager.EXTRA_PLUGGED, 0);
            if (chargePlug == BatteryManager.BATTERY_PLUGGED_WIRELESS) {
                status = 3;
            } else if (chargePlug == BatteryManager.BATTERY_PLUGGED_AC) {
                status = 4;
            } else if (!BaseActivity.activities.isEmpty() && BaseActivity.activities.get(BaseActivity.activities.size() - 1) instanceof TaskExecutingActivity) {
                status = 2;
            } else {
                status = 1;
            }
            int level = batteryStatusIntent.getIntExtra(BatteryManager.EXTRA_LEVEL, 0);

            //上报状态
            String hostname = Event.getOnHostnameEvent().hostname;

            if (TextUtils.isEmpty(hostname)) return;

            State state = new State(
                    hostname,
                    Event.getOnEmergencyStopEvent().emergencyStopState,
                    level,
                    status,
                    PackageUtils.getVersion(mApp));

            try {
                robotService.syncState(state, hostname).execute();
            } catch (Exception e) {
                e.printStackTrace();
            }

            List<Activity> activities = BaseActivity.activities;
            //正在创建任务或者更新任务不更新
            if (!activities.isEmpty() && activities.get(activities.size() - 1) instanceof TaskCreateActivity)
                return;

            //正在删除任务，删除对话框正在显示
            if (!activities.isEmpty() && activities.get(activities.size() - 1) instanceof ScheduledTaskListActivity && EasyDialog.isShow())
                return;

            if (System.currentTimeMillis() - Constant.lastUpdateTimeMills < 3000) return;

            if (1==1)
                return;
            //如果没登录，则不同步任务
            if (Constant.DEFAULT_USERNAME.equals(SpManager.getInstance().getString(Constant.USERNAME, Constant.DEFAULT_USERNAME)))
                return;

            try {
                //删除所有本地已经删除，但云端没有删除的任务
                List<Task> deletedTasks = dbRepository.getAllDeleteAndUnSyncTasks();
                Log.w("后台任务同步", "deleteTasks:" + deletedTasks.size());
                for (Task deletedTask : deletedTasks) {
                    try {
                        ServiceFactory.getRobotService().deleteOnlineTask(hostname, deletedTask.cloudId).execute();
                    } catch (Exception e) {
                        return;
                    }
                }

                //将云端没有，本地有的任务同步到云端
                List<Task> unCreatedTasks = dbRepository.getAllUnCreatedTasks();
                Log.w("后台任务同步", "unCreatedTasks:" + deletedTasks.size());
                for (Task unCreateTask : unCreatedTasks) {
                    try {
                        ServiceFactory.getRobotService().newOnlineTaskSync(hostname, OnlineTask.covertBy(unCreateTask)).execute();
                    } catch (Exception e) {
                        return;
                    }
                }

                //将本地更新的任务同步到云端
                List<Task> modifiedTasks = dbRepository.getAllModifiedTask();
                Log.w("后台任务同步", "modifiedTasks:" + deletedTasks.size());
                for (Task modifiedTask : modifiedTasks) {
                    try {
                        ServiceFactory.getRobotService().updateOnlineTaskSync(hostname, modifiedTask.cloudId, OnlineTask.covertBy(modifiedTask)).execute();
                    } catch (Exception e) {
                        return;
                    }
                }

                try {
                    //将云端任务拉取下来覆盖本地任务
                    TaskListResponse response = robotService.getAllOnlineTask(hostname).execute().body();
                    if (response == null || response.data == null || response.data.result == null)
                        return;
                    List<Task> list = new ArrayList<>();
                    for (TaskListResponse.DataDTO.ResultDTO resultDTO : response.data.result) {
                        list.add(TaskListResponse.convertTo(resultDTO));
                    }
                    dbRepository.deleteAllScheduledTasks();
                    dbRepository.insertAllScheduleTasks(list);

                    List<Task> allScheduledTaskSync = dbRepository.getAllScheduledTaskSync();
                    if (activities.get(activities.size() - 1) instanceof ScheduledTaskListActivity) {
                        Collections.sort(allScheduledTaskSync, (t1, t2) -> t2.startTime.compareTo(t1.startTime));
                        EventBus.getDefault().post(Event.getOnTaskUpdateEvent(allScheduledTaskSync));
                    }
                    if (activities.get(activities.size() - 1) instanceof MainActivity) {
                        controller.updateRecentTask(allScheduledTaskSync);
                    }
                } catch (Exception e) {
                    return;
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    };

    @Override
    public void onCreate() {
        ScheduledExecutorService scheduledExecutorService = Executors.newScheduledThreadPool(1);
        scheduledExecutorService.scheduleWithFixedDelay(task, 10, 15, TimeUnit.SECONDS);
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        return super.onStartCommand(intent, flags, startId);
    }

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }
}

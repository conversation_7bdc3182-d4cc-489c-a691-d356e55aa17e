package com.reeman.robot.disinfection.repository.dao;

import androidx.room.Dao;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;
import androidx.room.Update;

import com.reeman.robot.disinfection.repository.entities.Task;

import java.util.List;

import io.reactivex.rxjava3.core.Maybe;
import io.reactivex.rxjava3.core.Single;

@Dao
public interface TaskDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    Single<Long> newTask(Task task);

    @Query("SELECT * FROM T_TASK WHERE t_task_type = 0")
    Maybe<Task> getManualTask();

    @Update(onConflict = OnConflictStrategy.REPLACE)
    Single<Integer> updateTask(Task task);

    @Query("SELECT * FROM T_TASK WHERE t_task_type = 1 AND t_has_delete = 0 ORDER BY t_start_time DESC")
    Single<List<Task>> getAllScheduledTask();

    @Query("SELECT * FROM T_TASK WHERE t_task_type = 1 AND t_has_delete = 0")
    List<Task> getAllScheduledTaskSync();

    @Query("UPDATE T_TASK SET t_enabled = :enable, t_has_sync = 0 WHERE tid = :tid")
    Single<Integer> updateTaskEnableState(long tid, boolean enable);

    @Query("SELECT * FROM T_TASK WHERE t_task_type = 1 AND t_has_delete = 0 ORDER BY t_start_time DESC LIMIT 0, 1")
    Maybe<Task> getMostRecentTask();

    @Query("SELECT * FROM T_TASK WHERE t_task_type = 1 AND t_enabled = 1 AND t_repeat_time & :flag != 0 AND t_has_delete = 0 ORDER BY t_start_time DESC")
    Maybe<List<Task>> getCurrentScheduleTask(int flag);

    @Query("UPDATE T_TASK SET t_has_delete = 1, t_has_sync = 0 WHERE tid = :tid")
    Single<Integer> deleteTask(long tid);

    @Query("DELETE FROM T_TASK WHERE tid = :tid")
    Single<Integer> deleteTaskDirectly(long tid);

    @Query("SELECT * FROM T_TASK WHERE t_task_type = 1 AND t_has_sync = 0")
    List<Task> getAllUnSyncTask();

    @Query("UPDATE T_TASK SET t_has_sync = 1, t_cloud_id = :cloudId WHERE rowId = :row")
    void updateLocalTaskOnlineStateByRowId(long row, long cloudId);

    @Query("UPDATE T_TASK SET t_has_sync = 1, t_cloud_id = :cloudId WHERE tid = :tid")
    void updateLocalTaskOnlineStateByPrimaryKey(long tid, long cloudId);

    @Query("UPDATE T_TASK SET t_has_sync = 1 WHERE tid = :tid")
    void updateSyncStateByPrimaryKey(long tid);

    @Query("DELETE FROM T_TASK WHERE tid = :id")
    void deleteTaskDirectlySync(long id);

    @Query("SELECT * FROM T_TASK WHERE t_task_type = 1 AND t_has_delete = 1 AND t_has_sync = 0")
    List<Task> getAllDeleteAndUnSyncTasks();

    @Query("SELECT * FROM T_TASK WHERE t_task_type = 1 AND t_cloud_id = -1 AND t_has_sync = 0 AND t_has_delete = 0")
    List<Task> getAllUnCreatedTasks();

    @Query("SELECT * FROM T_TASK WHERE t_task_type = 1 AND t_cloud_id != -1 AND t_has_sync = 0 AND t_has_delete = 0")
    List<Task> getAllModifiedTask();

    @Query("DELETE FROM T_TASK WHERE t_task_type = 1")
    void deleteAllScheduledTasks();

    @Insert
    List<Long> insertAllScheduleTasks(List<Task> list);
}

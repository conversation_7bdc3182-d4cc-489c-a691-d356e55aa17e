package com.reeman.robot.disinfection.contract;

import com.reeman.robot.disinfection.presenter.IPresenter;
import com.reeman.robot.disinfection.repository.entities.Task;
import com.reeman.robot.disinfection.view.IView;

import java.util.List;

public interface ScheduledTaskListContract {

    interface Presenter extends IPresenter {

        void getAllScheduledTaskList();

        void updateTaskEnableState(int position, Task task, boolean enable);

        void deleteTask(int index, Task task);
    }

    interface View extends IView{

        void onScheduledTaskListLoaded(List<Task> tasks);

        void onScheduledTaskListLoadFailed(String error);

        void onTaskEnableStateUpdateSuccess(int position, boolean enable);

        void onTaskEnableStateUpdateFailed(String message);

        void onTaskDeleted(int index);
    }
}

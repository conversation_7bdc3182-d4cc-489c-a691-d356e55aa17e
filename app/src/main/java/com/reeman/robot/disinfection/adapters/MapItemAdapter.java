package com.reeman.robot.disinfection.adapters;

import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.reeman.robot.disinfection.R;
import com.reeman.robot.disinfection.request.model.MapListResponse;

import java.util.List;

public class MapItemAdapter extends RecyclerView.Adapter<MapItemAdapter.ViewHolder> {
    private final List<MapListResponse.Map> mapList;

    public MapItemAdapter(List<MapListResponse.Map> maps) {
        this.mapList = maps;
    }

    @NonNull
    @Override
    public MapItemAdapter.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        ViewGroup root = (ViewGroup) LayoutInflater.from(parent.getContext()).inflate(R.layout.layout_map_item, parent, false);
        return new ViewHolder(root);
    }

    @Override
    public void onBindViewHolder(@NonNull MapItemAdapter.ViewHolder holder, int position) {
        MapListResponse.Map map = mapList.get(position);
        if (map.selected) {
            holder.root.setBackgroundColor(Color.parseColor("#efefef"));
        } else {
            holder.root.setBackgroundColor(Color.WHITE);
        }
        holder.tvMapName.setText(map.name);
        holder.root.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                int adapterPosition = holder.getAdapterPosition();
                MapListResponse.Map item = mapList.get(adapterPosition);
                for (int i = 0; i < mapList.size(); i++) {
                    MapListResponse.Map temp = mapList.get(i);
                    if (item == temp) {
                        temp.selected = !temp.selected;
                    } else {
                        temp.selected = false;
                    }
                }
                notifyDataSetChanged();
            }
        });
    }

    @Override
    public int getItemCount() {
        return mapList.size();
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {

        private final TextView tvMapName;
        private final View root;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            this.root = itemView;
            tvMapName = this.itemView.findViewById(R.id.tv_spinner_item);
        }
    }
}

package com.reeman.robot.disinfection.request.model;

import com.google.gson.annotations.SerializedName;

public class TaskUpdateResponse {
    @SerializedName("code")
    public int code;
    @SerializedName("msg")
    public String msg;
    @SerializedName("data")
    public DataDTO data;

    public static class DataDTO {
        @SerializedName("result")
        public ResultDTO result;

        public static class ResultDTO {
            @SerializedName("id")
            public int id;
            @SerializedName("updateTime")
            public double updateTime;
            @SerializedName("disinfectionRobotId")
            public int disinfectionRobotId;
            @SerializedName("title")
            public String title;
            @SerializedName("disinfectionMode")
            public int disinfectionMode;
            @SerializedName("begin")
            public String begin;
            @SerializedName("repeat")
            public String repeat;
            @SerializedName("afterComplete")
            public int afterComplete;
            @SerializedName("disinfectionSwitch")
            public int disinfectionSwitch;
            @SerializedName("stayTime")
            public int stayTime;
            @SerializedName("cycleTime")
            public int cycleTime;
            @SerializedName("taskSwitch")
            public int taskSwitch;

            @Override
            public String toString() {
                final StringBuilder sb = new StringBuilder("ResultDTO{");
                sb.append("id=").append(id);
                sb.append(", updateTime=").append(updateTime);
                sb.append(", disinfectionRobotId=").append(disinfectionRobotId);
                sb.append(", title='").append(title).append('\'');
                sb.append(", disinfectionMode=").append(disinfectionMode);
                sb.append(", begin='").append(begin).append('\'');
                sb.append(", repeat='").append(repeat).append('\'');
                sb.append(", afterComplete=").append(afterComplete);
                sb.append(", disinfectionSwitch=").append(disinfectionSwitch);
                sb.append(", stayTime=").append(stayTime);
                sb.append(", cycleTime=").append(cycleTime);
                sb.append(", taskSwitch=").append(taskSwitch);
                sb.append('}');
                return sb.toString();
            }
        }

        @Override
        public String toString() {
            final StringBuilder sb = new StringBuilder("DataDTO{");
            sb.append("result=").append(result);
            sb.append('}');
            return sb.toString();
        }
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("TaskUpdateResponse{");
        sb.append("code=").append(code);
        sb.append(", msg='").append(msg).append('\'');
        sb.append(", data=").append(data);
        sb.append('}');
        return sb.toString();
    }
}

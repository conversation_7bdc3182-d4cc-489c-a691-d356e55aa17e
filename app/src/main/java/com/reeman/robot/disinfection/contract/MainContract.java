package com.reeman.robot.disinfection.contract;

import android.app.Activity;
import android.content.Context;

import com.reeman.robot.disinfection.annotations.CheckPermission;
import com.reeman.robot.disinfection.constants.Constant;
import com.reeman.robot.disinfection.presenter.IPresenter;
import com.reeman.robot.disinfection.repository.entities.Task;
import com.reeman.robot.disinfection.request.model.MapListResponse;
import com.reeman.robot.disinfection.view.IView;

import java.lang.reflect.Method;
import java.util.List;

public interface MainContract {
    interface Presenter extends IPresenter{

         Task getCurrentTask() ;

        void getManualTask();

        void getMostRecentTask();

        void onCheckPermission(Presenter presenter, Method method, Object[] args);

        void onGotoSettingPage(Context context);

        void onGotoTaskCreatePage(Context context, Task currentTask);

        void onGotoScheduledTaskListPage(Context context);

        void onGotoWiFiPage(Context context);

        void onGotoMapBuildingPage(Context context);

        @CheckPermission
        void onStartDisinfectionBtnClicked(Activity activity, boolean hasCheckDelay);

        @CheckPermission
        void onGotoCharge(Context context);

        void onChooseMap(Context context, String ipAddress);

        void onGotoElevatorSettingPage(Context context);

        void onNowMapNotDefaultMap(Context context,String nowMap);

    }

    interface View extends IView{

        void onManualTaskLoaded(Task task);

        void onMostRecentTaskLoaded(Task task);

        void showLockScreenView(Presenter presenter, Method method, Object[] args);

        void onChargeTaskStart();

        void onMapListLoaded(List<MapListResponse.Map> mapList,boolean reloc);

        void onMapListLoadedFailed(Throwable throwable);
    }

}

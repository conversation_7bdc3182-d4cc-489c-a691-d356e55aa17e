package com.reeman.robot.disinfection.utils;

import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.BatteryManager;

/**
 * @ClassName: BatteryUtils.java
 * @Author: <PERSON><PERSON><PERSON>ong(1123988589 @ qq.com)
 * @Date: 2022/1/9 20:17
 * @Description: 电池工具类
 */
public class BatteryUtils {

    /**
     * 获取电池相关信息
     *
     * @param context
     * @return
     */
    public static Intent getBatteryStatus(Context context) {
        IntentFilter intentFilter = new IntentFilter(Intent.ACTION_BATTERY_CHANGED);
        return context.registerReceiver(null, intentFilter);
    }

    /**
     * 是否正在充电
     *
     * @param context
     * @return
     */
    public static boolean isCharging(Context context) {
        IntentFilter intentFilter = new IntentFilter(Intent.ACTION_BATTERY_CHANGED);
        Intent batteryStatusIntent = context.registerReceiver(null, intentFilter);
        int chargePlug = batteryStatusIntent.getIntExtra(BatteryManager.EXTRA_PLUGGED, 0);
        return chargePlug == BatteryManager.BATTERY_PLUGGED_WIRELESS || chargePlug == BatteryManager.BATTERY_PLUGGED_AC;
    }

    /**
     * 是否是适配器充电
     *
     * @param context
     * @return
     */
    public static boolean isAcCharging(Context context) {
        IntentFilter intentFilter = new IntentFilter(Intent.ACTION_BATTERY_CHANGED);
        Intent batteryStatusIntent = context.registerReceiver(null, intentFilter);
        int chargePlug = batteryStatusIntent.getIntExtra(BatteryManager.EXTRA_PLUGGED, 0);
        return chargePlug == BatteryManager.BATTERY_PLUGGED_AC;
    }

    /**
     * 是否是充电桩充电
     *
     * @param context
     * @return
     */
    public static boolean isWirelessCharging(Context context) {
        IntentFilter intentFilter = new IntentFilter(Intent.ACTION_BATTERY_CHANGED);
        Intent batteryStatusIntent = context.registerReceiver(null, intentFilter);
        int chargePlug = batteryStatusIntent.getIntExtra(BatteryManager.EXTRA_PLUGGED, 0);
        return chargePlug == BatteryManager.BATTERY_PLUGGED_WIRELESS;
    }

    /**
     * 获取电池电量
     *
     * @param context
     * @return
     */
    public static int getPowerLevel(Context context) {
        IntentFilter intentFilter = new IntentFilter(Intent.ACTION_BATTERY_CHANGED);
        Intent batteryStatusIntent = context.registerReceiver(null, intentFilter);
        return batteryStatusIntent.getIntExtra(BatteryManager.EXTRA_LEVEL, 0);
    }
}

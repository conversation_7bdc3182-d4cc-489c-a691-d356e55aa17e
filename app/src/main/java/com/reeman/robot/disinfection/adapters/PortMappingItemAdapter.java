package com.reeman.robot.disinfection.adapters;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.reeman.robot.disinfection.R;
import com.reeman.robot.disinfection.request.model.PortMapping;
import com.reeman.robot.disinfection.widgets.EasyTextWatcher;

import java.util.List;

public class PortMappingItemAdapter extends RecyclerView.Adapter<PortMappingItemAdapter.ViewHolder> {

    private List<PortMapping> portMappingList;

    private PortMappingItemListener portMappingItemListener;

    public void setPortMappingItemListener(PortMappingItemListener portMappingItemListener){
        this.portMappingItemListener = portMappingItemListener;
    }

    public PortMappingItemAdapter(List<PortMapping> portMappingList) {
        this.portMappingList = portMappingList;
    }

    public List<PortMapping> getPortMappingList(){
        return portMappingList;
    }

    @NonNull
    @Override
    public PortMappingItemAdapter.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        ViewGroup root = (ViewGroup) LayoutInflater.from(parent.getContext()).inflate(R.layout.layout_port_mapping_item, parent, false);
        return new ViewHolder(root);
    }

    @Override
    public void onBindViewHolder(@NonNull  PortMappingItemAdapter.ViewHolder holder, int position) {
        PortMapping portMapping = portMappingList.get(position);
        portMapping.setPosition(position);
        holder.etFloor.setText(portMapping.getFloor()+"");
        holder.etPort.setText(portMapping.getPort()+"");
        holder.etFloor.addTextChangedListener(new EasyTextWatcher() {
            @Override
            public void onTextChanged(CharSequence charSequence) {
                if (charSequence.length()<1)return;
                portMapping.setFloor(Integer.parseInt(charSequence.toString()));
                for (int i = 0; i < portMappingList.size(); i++) {
                    if (portMappingList.get(i).getPosition() == portMapping.getPosition()) {
                        portMappingList.set(i, portMapping);
                    }
                }
            }
        });
        holder.etPort.addTextChangedListener(new EasyTextWatcher() {
            @Override
            public void onTextChanged(CharSequence charSequence) {
                if (charSequence.length()<1)return;
                portMapping.setPort(Integer.parseInt(charSequence.toString()));
                for (int i = 0; i < portMappingList.size(); i++) {
                    if (portMappingList.get(i).getPosition() == portMapping.getPosition()) {
                        portMappingList.set(i, portMapping);
                    }
                }
            }
        });
        holder.btnDelete.setOnClickListener(view -> {
            portMappingList.remove(position);
            notifyItemRemoved(position);
            notifyItemRangeChanged(position, portMappingList.size() - position);
        });
    }

    @Override
    public int getItemCount() {
        return portMappingList.size();
    }

    public interface PortMappingItemListener{
        void onChange(int position,PortMapping portMapping);
//        void onDelete(int position);

    }

    public static class ViewHolder extends RecyclerView.ViewHolder {

        private final EditText etPort,etFloor;
        private final Button btnDelete;

        public ViewHolder(@NonNull  View itemView) {
            super(itemView);
            etPort = this.itemView.findViewById(R.id.et_port);
            etFloor = this.itemView.findViewById(R.id.et_floor);
            btnDelete = this.itemView.findViewById(R.id.btn_delete);
        }
    }
}

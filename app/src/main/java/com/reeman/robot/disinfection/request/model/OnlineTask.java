package com.reeman.robot.disinfection.request.model;

import com.google.gson.Gson;
import com.google.gson.annotations.SerializedName;
import com.reeman.robot.disinfection.event.Event;
import com.reeman.robot.disinfection.repository.entities.Task;
import com.reeman.robot.disinfection.utils.TimeUtils;

public class OnlineTask {
    @SerializedName("name")
    public String name;
    @SerializedName("taskId")
    public long taskId;
    @SerializedName("title")
    public String title;
    @SerializedName("disinfectionMode")
    public int disinfectionMode;
    @SerializedName("begin")
    public String begin;
    @SerializedName("repeat")
    public String repeat;
    @SerializedName("afterComplete")
    public int afterComplete;
    @SerializedName("disinfectionSwitch")
    public int disinfectionSwitch;
    @SerializedName("stayTime")
    public long stayTime;
    @SerializedName("cycleTime")
    public long cycleTime;
    @SerializedName("taskSwitch")
    public int taskSwitch;


    public OnlineTask(long id, String name, String title, int disinfectionMode, String begin, String repeat, int afterComplete, int disinfectionSwitch, long stayTime, long cycleTime, int taskSwitch) {
        this.taskId = id;
        this.name = name;
        this.title = title;
        this.disinfectionMode = disinfectionMode;
        this.begin = begin;
        this.repeat = repeat;
        this.afterComplete = afterComplete;
        this.disinfectionSwitch = disinfectionSwitch;
        this.stayTime = stayTime;
        this.cycleTime = cycleTime;
        this.taskSwitch = taskSwitch;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("OnlineTask{");
        sb.append("name='").append(name).append('\'');
        sb.append(", taskId=").append(taskId);
        sb.append(", title='").append(title).append('\'');
        sb.append(", disinfectionMode=").append(disinfectionMode);
        sb.append(", begin='").append(begin).append('\'');
        sb.append(", repeat='").append(repeat).append('\'');
        sb.append(", afterComplete=").append(afterComplete);
        sb.append(", disinfectionSwitch=").append(disinfectionSwitch);
        sb.append(", stayTime=").append(stayTime);
        sb.append(", cycleTime=").append(cycleTime);
        sb.append(", taskSwitch=").append(taskSwitch);
        sb.append('}');
        return sb.toString();
    }

    public static OnlineTask covertBy(Task task) {
        WeekData weekData = new WeekData();
        weekData.setMonday((task.repeatTime & 0x01) != 0 ? 1 : 0);
        weekData.setTuesday((task.repeatTime & 0x02) != 0 ? 1 : 0);
        weekData.setWednesday((task.repeatTime & 0x04) != 0 ? 1 : 0);
        weekData.setThursday((task.repeatTime & 0x08) != 0 ? 1 : 0);
        weekData.setFriday((task.repeatTime & 0x10) != 0 ? 1 : 0);
        weekData.setSaturday((task.repeatTime & 0x20) != 0 ? 1 : 0);
        weekData.setSunday((task.repeatTime & 0x40) != 0 ? 1 : 0);
        return new OnlineTask(
                task.cloudId,
                Event.getOnHostnameEvent().hostname,
                task.taskName,
                task.taskMode + 1,
                task.startTime,
                new Gson().toJson(weekData),
                task.finishAction == 0 ? 3 : 2,
                task.switchMode == 0 ? 2 : (task.switchMode == 1 ? 1 : 0),
                task.stayTime,
                task.durationTime,
                task.enabled ? 1 : 0
        );
    }
}

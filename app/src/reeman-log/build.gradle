plugins {
    id 'com.android.library'
    id 'maven-publish'
}

android {
    namespace 'com.reeman.log'
    compileSdk 33

    defaultConfig {
        minSdkVersion 21
        versionCode 1
        versionName "1.0.0"
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

task generateSourcesJar(type: Jar){
    from android.sourceSets.main.java.srcDirs
    classifier  'sources'
}

dependencies {
    api 'com.jakewharton.timber:timber:5.0.1'
    api 'com.elvishew:xlog:1.10.1'
}



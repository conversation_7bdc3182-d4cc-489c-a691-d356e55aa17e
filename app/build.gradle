import java.text.SimpleDateFormat

plugins {
    id 'com.android.application'
}

android {
    compileSdkVersion 33
    buildToolsVersion "30.0.3"

    defaultConfig {
        applicationId "com.reeman.robot.disinfection"
        minSdkVersion 21 // UPDATED from 19 to 21
        targetSdkVersion 30
        versionCode 325
        versionName "v3.2.5_elevator"
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        buildConfigField 'String', 'TAG', '"xuedong"'
        buildConfigField 'String', 'BUGLY', '"fec460176e"'
        multiDexEnabled true // ADDED for dex limit

        ndk {
            abiFilters 'armeabi', 'armeabi-v7a'
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    // ADDED for native lib conflict
    packagingOptions {
        pickFirst 'lib/armeabi-v7a/libserial_port.so'
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    sourceSets {
        main {
            jniLibs.srcDirs = ['jniLibs']
        }
    }
    namespace 'com.reeman.robot.disinfection'
    android.applicationVariants.all {
        variant ->
            variant.outputs.all {
                outputFileName = "disinfection_kagao_${defaultConfig.versionName}-${getCurrentTime()}.apk"
            }
    }
}

def static getCurrentTime() {
    SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmm");
    Date curDate = new Date(System.currentTimeMillis());
    return formatter.format(curDate);
}

/*repositories {
    maven { url "https://jitpack.io" }
    mavenCentral()
}*/

dependencies {
    implementation 'androidx.multidex:multidex:2.0.1' // ADDED for dex limit

    implementation 'com.github.Misaka-XXXXII:reeman-lib:1.1.0'
    implementation 'com.github.Misaka-XXXXII.reeman-lib:serialport:1.1.0'
    implementation fileTree(dir: 'libs', include: ['*.jar','*.aar'])
    // implementation 'com.gongwen.marqueen:marqueen:1.1.1'  // Temporarily commented out - missing dependency
    // UPDATED dependencies to reduce method count
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.9.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'

    implementation 'com.elvishew:xlog:1.10.1'
    implementation 'pl.droidsonroids.gif:android-gif-drawable:1.2.22'
    implementation 'com.tencent.bugly:nativecrashreport:3.9.2'
    implementation 'com.tencent.bugly:crashreport_upgrade:1.6.1'
    implementation 'com.google.code.gson:gson:2.8.6'
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.9.0'
    implementation 'com.squareup.retrofit2:adapter-rxjava3:2.9.0'
    implementation 'io.reactivex.rxjava3:rxjava:3.0.12'
    implementation 'io.reactivex.rxjava3:rxandroid:3.0.0'
    implementation "androidx.room:room-rxjava3:2.3.0"
    implementation "androidx.room:room-runtime:2.3.0"
    annotationProcessor "androidx.room:room-compiler:2.3.0"
    implementation "androidx.swiperefreshlayout:swiperefreshlayout:1.0.0"
    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
    implementation 'com.kyleduo.switchbutton:library:2.1.0'
    implementation 'com.github.ybq:Android-SpinKit:1.4.0'
    implementation "org.greenrobot:eventbus:3.3.1"
    implementation 'com.google.zxing:core:3.3.0'
    implementation 'io.jsonwebtoken:jjwt-api:0.11.2'
    runtimeOnly 'io.jsonwebtoken:jjwt-impl:0.11.2'
    runtimeOnly('io.jsonwebtoken:jjwt-orgjson:0.11.2') {
        exclude group: 'org.json', module: 'json'
    }
    implementation 'com.microsoft.cognitiveservices.speech:client-sdk:1.19.0'
    testImplementation 'junit:junit:4.+'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
    implementation 'com.github.licheedev:Android-SerialPort-API:2.0.0'
}
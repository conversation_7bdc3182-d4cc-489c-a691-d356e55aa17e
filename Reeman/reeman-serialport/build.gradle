plugins {
    id 'com.android.library'
    id 'maven-publish'
}

android {
    compileSdkVersion 33
    namespace 'com.reeman.serialport'

    defaultConfig {
        minSdkVersion 21
        buildConfigField 'String', 'LOG_ROS', '"ros"'
        buildConfigField 'String', 'LOG_POWER_BOARD', '"power_board_log"'
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    sourceSets {
        main {
            jniLibs.srcDirs = ['libs']
        }
    }
}

task generateSourcesJar(type: Jar){
    from android.sourceSets.main.java.srcDirs
    classifier  'sources'
}

dependencies {
    implementation project(':reeman-log')
}

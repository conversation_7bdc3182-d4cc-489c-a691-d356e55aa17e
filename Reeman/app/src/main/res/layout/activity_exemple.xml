<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorPrimary"
    tools:context=".ExempleActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_core_data"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="10dp"
                android:background="@color/view_bg"
                android:gravity="center"
                android:padding="10dp"
                android:text="@string/text_core_data"
                android:textColor="@color/white"
                android:textSize="20sp" />

            <TextView
                android:id="@+id/tv_refresh_hostname"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="10dp"
                android:background="@color/view_bg"
                android:gravity="center"
                android:onClick="refreshHostname"
                android:padding="10dp"
                android:text="@string/text_refresh_hostname"
                android:textColor="@color/white"
                android:textSize="20sp" />

            <TextView
                android:id="@+id/tv_refresh_ip"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="10dp"
                android:background="@color/view_bg"
                android:gravity="center"
                android:onClick="refreshIP"
                android:padding="10dp"
                android:text="@string/text_refresh_ip"
                android:textColor="@color/white"
                android:textSize="20sp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="10dp"
                android:background="@color/view_bg"
                android:gravity="center"
                android:onClick="navigationTOPointA"
                android:padding="10dp"
                android:text="@string/text_navigation_to_point_a"
                android:textColor="@color/white"
                android:textSize="20sp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="10dp"
                android:background="@color/view_bg"
                android:gravity="center"
                android:onClick="cancelNavigation"
                android:padding="10dp"
                android:text="@string/text_cancel_navigation"
                android:textColor="@color/white"
                android:textSize="20sp" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="10dp"
                android:background="@color/view_bg"
                android:gravity="center"
                android:onClick="exit"
                android:padding="10dp"
                android:text="@string/text_exit"
                android:textColor="@color/white"
                android:textSize="20sp" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:layout_weight="1">

            <TextView
                android:id="@+id/tv_ros_data"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:layout_margin="10dp"
                android:padding="10dp"
                android:scrollbars="vertical"
                android:textColor="@color/white"
                android:textSize="15sp" />


            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="10dp"
                android:layout_gravity="end"
                android:layout_marginEnd="20dp"
                android:onClick="clean"
                android:textColor="@color/white"
                android:text="@string/text_clean"
                android:textSize="25sp" />

        </LinearLayout>

    </LinearLayout>


</LinearLayout>